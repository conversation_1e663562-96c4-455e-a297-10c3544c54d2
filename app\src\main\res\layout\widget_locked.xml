<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:id="@+id/Layout"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:background="?android:colorBackground">

  <TextView
    android:id="@+id/Text"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center"
    android:layout_gravity="center"
    android:text="@string/locked"
    android:textSize="18sp"
    />

</FrameLayout>
