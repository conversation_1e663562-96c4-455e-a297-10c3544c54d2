package com.rodrigmatrix.weatheryou.components.theme

import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.ui.graphics.Color

private val primaryLight = Color(0xFFA40314)
private val onPrimaryLight = Color(0xFFFFFFFF)
private val primaryContainerLight = Color(0xFFD93533)
private val onPrimaryContainerLight = Color(0xFFFFFFFF)
private val secondaryLight = Color(0xFF9F3F39)
private val onSecondaryLight = Color(0xFFFFFFFF)
private val secondaryContainerLight = Color(0xFFFF9990)
private val onSecondaryContainerLight = Color(0xFF57090A)
private val tertiaryLight = Color(0xFF734600)
private val onTertiaryLight = Color(0xFFFFFFFF)
private val tertiaryContainerLight = Color(0xFFA46700)
private val onTertiaryContainerLight = Color(0xFFFFFFFF)
private val errorLight = Color(0xFFBA1A1A)
private val onErrorLight = Color(0xFFFFFFFF)
private val errorContainerLight = Color(0xFFFFDAD6)
private val onErrorContainerLight = Color(0xFF410002)
private val backgroundLight = Color(0xFFFFF8F7)
private val onBackgroundLight = Color(0xFF271816)
private val surfaceLight = Color(0xFFFFF8F7)
private val onSurfaceLight = Color(0xFF271816)
private val surfaceVariantLight = Color(0xFFFFDAD6)
private val onSurfaceVariantLight = Color(0xFF5B403D)
private val outlineLight = Color(0xFF8F6F6C)
private val outlineVariantLight = Color(0xFFE4BEBA)
private val scrimLight = Color(0xFF000000)
private val inverseSurfaceLight = Color(0xFF3E2C2A)
private val inverseOnSurfaceLight = Color(0xFFFFEDEB)
private val inversePrimaryLight = Color(0xFFFFB3AC)
private val surfaceDimLight = Color(0xFFF1D3D0)
private val surfaceBrightLight = Color(0xFFFFF8F7)
private val surfaceContainerLowestLight = Color(0xFFFFFFFF)
private val surfaceContainerLowLight = Color(0xFFFFF0EF)
private val surfaceContainerLight = Color(0xFFFFE9E6)
private val surfaceContainerHighLight = Color(0xFFFFE2DE)
private val surfaceContainerHighestLight = Color(0xFFF9DCD9)
private val primaryLightMediumContrast = Color(0xFF8B000E)
private val onPrimaryLightMediumContrast = Color(0xFFFFFFFF)
private val primaryContainerLightMediumContrast = Color(0xFFD93533)
private val onPrimaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val secondaryLightMediumContrast = Color(0xFF7B2421)
private val onSecondaryLightMediumContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightMediumContrast = Color(0xFFBC554D)
private val onSecondaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryLightMediumContrast = Color(0xFF603A00)
private val onTertiaryLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightMediumContrast = Color(0xFFA46700)
private val onTertiaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val errorLightMediumContrast = Color(0xFF8C0009)
private val onErrorLightMediumContrast = Color(0xFFFFFFFF)
private val errorContainerLightMediumContrast = Color(0xFFDA342E)
private val onErrorContainerLightMediumContrast = Color(0xFFFFFFFF)
private val backgroundLightMediumContrast = Color(0xFFFFF8F7)
private val onBackgroundLightMediumContrast = Color(0xFF271816)
private val surfaceLightMediumContrast = Color(0xFFFFF8F7)
private val onSurfaceLightMediumContrast = Color(0xFF271816)
private val surfaceVariantLightMediumContrast = Color(0xFFFFDAD6)
private val onSurfaceVariantLightMediumContrast = Color(0xFF573C3A)
private val outlineLightMediumContrast = Color(0xFF755855)
private val outlineVariantLightMediumContrast = Color(0xFF937370)
private val scrimLightMediumContrast = Color(0xFF000000)
private val inverseSurfaceLightMediumContrast = Color(0xFF3E2C2A)
private val inverseOnSurfaceLightMediumContrast = Color(0xFFFFEDEB)
private val inversePrimaryLightMediumContrast = Color(0xFFFFB3AC)
private val surfaceDimLightMediumContrast = Color(0xFFF1D3D0)
private val surfaceBrightLightMediumContrast = Color(0xFFFFF8F7)
private val surfaceContainerLowestLightMediumContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightMediumContrast = Color(0xFFFFF0EF)
private val surfaceContainerLightMediumContrast = Color(0xFFFFE9E6)
private val surfaceContainerHighLightMediumContrast = Color(0xFFFFE2DE)
private val surfaceContainerHighestLightMediumContrast = Color(0xFFF9DCD9)
private val primaryLightHighContrast = Color(0xFF4E0004)
private val onPrimaryLightHighContrast = Color(0xFFFFFFFF)
private val primaryContainerLightHighContrast = Color(0xFF8B000E)
private val onPrimaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val secondaryLightHighContrast = Color(0xFF4D0205)
private val onSecondaryLightHighContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightHighContrast = Color(0xFF7B2421)
private val onSecondaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryLightHighContrast = Color(0xFF341D00)
private val onTertiaryLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightHighContrast = Color(0xFF603A00)
private val onTertiaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val errorLightHighContrast = Color(0xFF4E0002)
private val onErrorLightHighContrast = Color(0xFFFFFFFF)
private val errorContainerLightHighContrast = Color(0xFF8C0009)
private val onErrorContainerLightHighContrast = Color(0xFFFFFFFF)
private val backgroundLightHighContrast = Color(0xFFFFF8F7)
private val onBackgroundLightHighContrast = Color(0xFF271816)
private val surfaceLightHighContrast = Color(0xFFFFF8F7)
private val onSurfaceLightHighContrast = Color(0xFF000000)
private val surfaceVariantLightHighContrast = Color(0xFFFFDAD6)
private val onSurfaceVariantLightHighContrast = Color(0xFF341E1C)
private val outlineLightHighContrast = Color(0xFF573C3A)
private val outlineVariantLightHighContrast = Color(0xFF573C3A)
private val scrimLightHighContrast = Color(0xFF000000)
private val inverseSurfaceLightHighContrast = Color(0xFF3E2C2A)
private val inverseOnSurfaceLightHighContrast = Color(0xFFFFFFFF)
private val inversePrimaryLightHighContrast = Color(0xFFFFE7E4)
private val surfaceDimLightHighContrast = Color(0xFFF1D3D0)
private val surfaceBrightLightHighContrast = Color(0xFFFFF8F7)
private val surfaceContainerLowestLightHighContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightHighContrast = Color(0xFFFFF0EF)
private val surfaceContainerLightHighContrast = Color(0xFFFFE9E6)
private val surfaceContainerHighLightHighContrast = Color(0xFFFFE2DE)
private val surfaceContainerHighestLightHighContrast = Color(0xFFF9DCD9)
private val primaryDark = Color(0xFFFFB3AC)
private val onPrimaryDark = Color(0xFF680008)
private val primaryContainerDark = Color(0xFFD93533)
private val onPrimaryContainerDark = Color(0xFFFFFFFF)
private val secondaryDark = Color(0xFFFFB3AC)
private val onSecondaryDark = Color(0xFF611111)
private val secondaryContainerDark = Color(0xFF741F1C)
private val onSecondaryContainerDark = Color(0xFFFFC3BD)
private val tertiaryDark = Color(0xFFFFB962)
private val onTertiaryDark = Color(0xFF472A00)
private val tertiaryContainerDark = Color(0xFFA46700)
private val onTertiaryContainerDark = Color(0xFFFFFFFF)
private val errorDark = Color(0xFFFFB4AB)
private val onErrorDark = Color(0xFF690005)
private val errorContainerDark = Color(0xFF93000A)
private val onErrorContainerDark = Color(0xFFFFDAD6)
private val backgroundDark = Color(0xFF1E100E)
private val onBackgroundDark = Color(0xFFF9DCD9)
private val surfaceDark = Color(0xFF1E100E)
private val onSurfaceDark = Color(0xFFF9DCD9)
private val surfaceVariantDark = Color(0xFF5B403D)
private val onSurfaceVariantDark = Color(0xFFE4BEBA)
private val outlineDark = Color(0xFFAB8985)
private val outlineVariantDark = Color(0xFF5B403D)
private val scrimDark = Color(0xFF000000)
private val inverseSurfaceDark = Color(0xFFF9DCD9)
private val inverseOnSurfaceDark = Color(0xFF3E2C2A)
private val inversePrimaryDark = Color(0xFFB91B20)
private val surfaceDimDark = Color(0xFF1E100E)
private val surfaceBrightDark = Color(0xFF473533)
private val surfaceContainerLowestDark = Color(0xFF180A09)
private val surfaceContainerLowDark = Color(0xFF271816)
private val surfaceContainerDark = Color(0xFF2C1C1A)
private val surfaceContainerHighDark = Color(0xFF372624)
private val surfaceContainerHighestDark = Color(0xFF43302E)
private val primaryDarkMediumContrast = Color(0xFFFFBAB3)
private val onPrimaryDarkMediumContrast = Color(0xFF370002)
private val primaryContainerDarkMediumContrast = Color(0xFFFF544E)
private val onPrimaryContainerDarkMediumContrast = Color(0xFF000000)
private val secondaryDarkMediumContrast = Color(0xFFFFBAB3)
private val onSecondaryDarkMediumContrast = Color(0xFF370002)
private val secondaryContainerDarkMediumContrast = Color(0xFFDF7067)
private val onSecondaryContainerDarkMediumContrast = Color(0xFF000000)
private val tertiaryDarkMediumContrast = Color(0xFFFFBE71)
private val onTertiaryDarkMediumContrast = Color(0xFF241200)
private val tertiaryContainerDarkMediumContrast = Color(0xFFCA800C)
private val onTertiaryContainerDarkMediumContrast = Color(0xFF000000)
private val errorDarkMediumContrast = Color(0xFFFFBAB1)
private val onErrorDarkMediumContrast = Color(0xFF370001)
private val errorContainerDarkMediumContrast = Color(0xFFFF5449)
private val onErrorContainerDarkMediumContrast = Color(0xFF000000)
private val backgroundDarkMediumContrast = Color(0xFF1E100E)
private val onBackgroundDarkMediumContrast = Color(0xFFF9DCD9)
private val surfaceDarkMediumContrast = Color(0xFF1E100E)
private val onSurfaceDarkMediumContrast = Color(0xFFFFF9F9)
private val surfaceVariantDarkMediumContrast = Color(0xFF5B403D)
private val onSurfaceVariantDarkMediumContrast = Color(0xFFE8C2BE)
private val outlineDarkMediumContrast = Color(0xFFBE9B97)
private val outlineVariantDarkMediumContrast = Color(0xFF9C7B78)
private val scrimDarkMediumContrast = Color(0xFF000000)
private val inverseSurfaceDarkMediumContrast = Color(0xFFF9DCD9)
private val inverseOnSurfaceDarkMediumContrast = Color(0xFF372624)
private val inversePrimaryDarkMediumContrast = Color(0xFF950010)
private val surfaceDimDarkMediumContrast = Color(0xFF1E100E)
private val surfaceBrightDarkMediumContrast = Color(0xFF473533)
private val surfaceContainerLowestDarkMediumContrast = Color(0xFF180A09)
private val surfaceContainerLowDarkMediumContrast = Color(0xFF271816)
private val surfaceContainerDarkMediumContrast = Color(0xFF2C1C1A)
private val surfaceContainerHighDarkMediumContrast = Color(0xFF372624)
private val surfaceContainerHighestDarkMediumContrast = Color(0xFF43302E)
private val primaryDarkHighContrast = Color(0xFFFFF9F9)
private val onPrimaryDarkHighContrast = Color(0xFF000000)
private val primaryContainerDarkHighContrast = Color(0xFFFFBAB3)
private val onPrimaryContainerDarkHighContrast = Color(0xFF000000)
private val secondaryDarkHighContrast = Color(0xFFFFF9F9)
private val onSecondaryDarkHighContrast = Color(0xFF000000)
private val secondaryContainerDarkHighContrast = Color(0xFFFFBAB3)
private val onSecondaryContainerDarkHighContrast = Color(0xFF000000)
private val tertiaryDarkHighContrast = Color(0xFFFFFAF8)
private val onTertiaryDarkHighContrast = Color(0xFF000000)
private val tertiaryContainerDarkHighContrast = Color(0xFFFFBE71)
private val onTertiaryContainerDarkHighContrast = Color(0xFF000000)
private val errorDarkHighContrast = Color(0xFFFFF9F9)
private val onErrorDarkHighContrast = Color(0xFF000000)
private val errorContainerDarkHighContrast = Color(0xFFFFBAB1)
private val onErrorContainerDarkHighContrast = Color(0xFF000000)
private val backgroundDarkHighContrast = Color(0xFF1E100E)
private val onBackgroundDarkHighContrast = Color(0xFFF9DCD9)
private val surfaceDarkHighContrast = Color(0xFF1E100E)
private val onSurfaceDarkHighContrast = Color(0xFFFFFFFF)
private val surfaceVariantDarkHighContrast = Color(0xFF5B403D)
private val onSurfaceVariantDarkHighContrast = Color(0xFFFFF9F9)
private val outlineDarkHighContrast = Color(0xFFE8C2BE)
private val outlineVariantDarkHighContrast = Color(0xFFE8C2BE)
private val scrimDarkHighContrast = Color(0xFF000000)
private val inverseSurfaceDarkHighContrast = Color(0xFFF9DCD9)
private val inverseOnSurfaceDarkHighContrast = Color(0xFF000000)
private val inversePrimaryDarkHighContrast = Color(0xFF5C0006)
private val surfaceDimDarkHighContrast = Color(0xFF1E100E)
private val surfaceBrightDarkHighContrast = Color(0xFF473533)
private val surfaceContainerLowestDarkHighContrast = Color(0xFF180A09)
private val surfaceContainerLowDarkHighContrast = Color(0xFF271816)
private val surfaceContainerDarkHighContrast = Color(0xFF2C1C1A)
private val surfaceContainerHighDarkHighContrast = Color(0xFF372624)
private val surfaceContainerHighestDarkHighContrast = Color(0xFF43302E)

val carmineLightScheme = lightColorScheme(
    primary = primaryLight,
    onPrimary = onPrimaryLight,
    primaryContainer = primaryContainerLight,
    onPrimaryContainer = onPrimaryContainerLight,
    secondary = secondaryLight,
    onSecondary = onSecondaryLight,
    secondaryContainer = secondaryContainerLight,
    onSecondaryContainer = onSecondaryContainerLight,
    tertiary = tertiaryLight,
    onTertiary = onTertiaryLight,
    tertiaryContainer = tertiaryContainerLight,
    onTertiaryContainer = onTertiaryContainerLight,
    error = errorLight,
    onError = onErrorLight,
    errorContainer = errorContainerLight,
    onErrorContainer = onErrorContainerLight,
    background = backgroundLight,
    onBackground = onBackgroundLight,
    surface = surfaceLight,
    onSurface = onSurfaceLight,
    surfaceVariant = surfaceVariantLight,
    onSurfaceVariant = onSurfaceVariantLight,
    outline = outlineLight,
    outlineVariant = outlineVariantLight,
    scrim = scrimLight,
    inverseSurface = inverseSurfaceLight,
    inverseOnSurface = inverseOnSurfaceLight,
    inversePrimary = inversePrimaryLight,
    surfaceDim = surfaceDimLight,
    surfaceBright = surfaceBrightLight,
    surfaceContainerLowest = surfaceContainerLowestLight,
    surfaceContainerLow = surfaceContainerLowLight,
    surfaceContainer = surfaceContainerLight,
    surfaceContainerHigh = surfaceContainerHighLight,
    surfaceContainerHighest = surfaceContainerHighestLight,
)

val carmineDarkScheme = darkColorScheme(
    primary = primaryDark,
    onPrimary = onPrimaryDark,
    primaryContainer = primaryContainerDark,
    onPrimaryContainer = onPrimaryContainerDark,
    secondary = secondaryDark,
    onSecondary = onSecondaryDark,
    secondaryContainer = secondaryContainerDark,
    onSecondaryContainer = onSecondaryContainerDark,
    tertiary = tertiaryDark,
    onTertiary = onTertiaryDark,
    tertiaryContainer = tertiaryContainerDark,
    onTertiaryContainer = onTertiaryContainerDark,
    error = errorDark,
    onError = onErrorDark,
    errorContainer = errorContainerDark,
    onErrorContainer = onErrorContainerDark,
    background = backgroundDark,
    onBackground = onBackgroundDark,
    surface = surfaceDark,
    onSurface = onSurfaceDark,
    surfaceVariant = surfaceVariantDark,
    onSurfaceVariant = onSurfaceVariantDark,
    outline = outlineDark,
    outlineVariant = outlineVariantDark,
    scrim = scrimDark,
    inverseSurface = inverseSurfaceDark,
    inverseOnSurface = inverseOnSurfaceDark,
    inversePrimary = inversePrimaryDark,
    surfaceDim = surfaceDimDark,
    surfaceBright = surfaceBrightDark,
    surfaceContainerLowest = surfaceContainerLowestDark,
    surfaceContainerLow = surfaceContainerLowDark,
    surfaceContainer = surfaceContainerDark,
    surfaceContainerHigh = surfaceContainerHighDark,
    surfaceContainerHighest = surfaceContainerHighestDark,
)