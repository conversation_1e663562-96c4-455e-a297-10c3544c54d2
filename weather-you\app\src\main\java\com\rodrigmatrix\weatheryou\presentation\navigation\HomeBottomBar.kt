package com.rodrigmatrix.weatheryou.presentation.navigation

import androidx.compose.material3.Icon
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavController
import androidx.navigation.compose.currentBackStackEntryAsState
import com.rodrigmatrix.weatheryou.home.presentation.navigation.HomeEntry

@Composable
fun HomeBottomBar(
    navController: NavController,
    entries: Array<HomeEntry> = HomeEntry.values(),
    onNavigationItemClick: (HomeEntry) -> Unit
) {
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination
    NavigationBar {
        entries.forEach { screen ->
            NavigationBarItem(
                icon = { Icon(painterResource(screen.icon), contentDescription = null) },
                label = { Text(stringResource(screen.stringRes)) },
                onClick = { onNavigationItemClick(screen) },
                selected = currentDestination?.route == screen.route
            )
        }
    }
}
