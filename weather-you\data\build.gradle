plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'kotlinx-serialization'
}

android {
    namespace 'com.rodrigmatrix.weatheryou.data'
    compileSdk Sdk.compileSdk

    buildFeatures {
        buildConfig = true
    }

    defaultConfig {
        minSdk Sdk.phoneMinSdk
        targetSdk Sdk.targetSdk

        def localProperties = new Properties()
        if (rootProject.file("local.properties").exists()) {
            localProperties.load(new FileInputStream(rootProject.file("local.properties")))
        }
        def visualCodingToken = System.getenv("VISUAL_CODING_TOKEN") ?: localProperties['visualcoding.token']
        def openWeatherToken = System.getenv("OPEN_WEATHER_TOKEN") ?: localProperties['openweather.token']
        def googleMapsToken = System.getenv("GOOGLE_MAPS_TOKEN") ?: localProperties['googlemaps.token']
        def apiNinjasToken = System.getenv("API_NINJAS_TOKEN") ?: localProperties['apininjas.token']
        def weatherKitToken = System.getenv("WEATHERKIT_TOKEN") ?: localProperties['weatherkit.token']
        def weatherKitId = System.getenv("WEATHERKIT_ID") ?: localProperties['weatherkit.id']
        def weatherKitKid = System.getenv("WEATHERKIT_KID") ?: localProperties['weatherkit.kid']
        def weatherKitISS = System.getenv("WEATHERKIT_ISS") ?: localProperties['weatherkit.iss']
        def weatherKitSub = System.getenv("WEATHERKIT_SUB") ?: localProperties['weatherkit.sub']
        def locationIqToken = System.getenv("LOCATIONIQ_TOKEN") ?: localProperties['locationiq.token']

        buildConfigField "String", "VISUAL_CODING_URL", "\"https://weather.visualcrossing.com/VisualCrossingWebServices/rest/services/\""
        buildConfigField "String", "VISUAL_CODING_TOKEN", "\"" + visualCodingToken + "\""

        buildConfigField "String", "OPEN_WEATHER_URL", "\"https://api.openweathermap.org/data/2.5/\""
        buildConfigField "String", "OPEN_WEATHER_TOKEN", "\"" + openWeatherToken + "\""

        buildConfigField "String", "GOOGLE_MAPS_URL", "\"https://maps.googleapis.com/maps/api/\""
        buildConfigField "String", "GOOGLE_MAPS_TOKEN", "\"" + googleMapsToken + "\""

        buildConfigField "String", "API_NINJAS_URL", "\"https://api.api-ninjas.com/\""
        buildConfigField "String", "API_NINJAS_TOKEN", "\"" + apiNinjasToken + "\""

        buildConfigField "String", "WEATHER_KIT_URL", "\"https://weatherkit.apple.com/api/\""
        buildConfigField "String", "WEATHER_KIT_TOKEN", "\"" + weatherKitToken + "\""
        buildConfigField "String", "WEATHER_KIT_ID", "\"" + weatherKitId + "\""
        buildConfigField "String", "WEATHER_KIT_KID", "\"" + weatherKitKid + "\""
        buildConfigField "String", "WEATHER_KIT_ISS", "\"" + weatherKitISS + "\""
        buildConfigField "String", "WEATHER_KIT_SUB", "\"" + weatherKitSub + "\""

        buildConfigField "String", "LOCATION_IQ_URL", "\"https://api.locationiq.com/v1/\""
        buildConfigField "String", "LOCATION_IQ_TOKEN", "\"" + locationIqToken + "\""

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"

        javaCompileOptions {
            annotationProcessorOptions {
                arguments += ["room.schemaLocation": "$projectDir/schemas".toString()]
            }
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    kotlin.sourceSets.configureEach {
        languageSettings.optIn("kotlin.RequiresOptIn")
    }
}

dependencies {
    implementation project(LocalModules.core)
    implementation project(LocalModules.domain)
    implementation project(LocalModules.weatherIcons)

    implementation(libs.androidx.ktx)
    implementation(libs.androidx.lifecycle)
    implementation(libs.androidx.window)

    implementation(libs.kotlin.stdlib)
    implementation(libs.kotlin.coroutines.android)
    implementation(libs.kotlin.serialization)
    implementation(libs.coroutines.playServices)

    implementation(libs.jwt.api)
    runtimeOnly(libs.jwt.impl)
    runtimeOnly(libs.jwt.jackson)

    implementation platform(libs.firebase.bom)
    implementation(libs.firebase.remote.config)
    implementation(libs.firebase.analytics)
    implementation(libs.firebase.database)

    implementation(libs.room.runtime)
    kapt(libs.room.compiler)
    implementation(libs.room.ktx)

    implementation(libs.workManager)

    implementation(libs.datastore.core)
    implementation(libs.datastore.preferences)

    implementation(libs.retrofit)
    implementation(platform(libs.okHttp))
    implementation(libs.retrofit.kotlinx.converter)
    implementation(libs.okHttp.interceptor)

    implementation(libs.koin.android)

    implementation(libs.jodaTime)

    implementation(libs.google.play.location)

    testImplementation(libs.junit)
    testImplementation(libs.mockk)
    testImplementation(libs.turbine)
}