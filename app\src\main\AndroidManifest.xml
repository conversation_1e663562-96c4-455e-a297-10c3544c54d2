<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:installLocation="auto">

    <uses-permission
        android:name="android.permission.ACCESS_NETWORK_STATE"
        tools:node="remove" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />

    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />

    <application
        android:name=".NotallyXApplication"
        android:allowBackup="true"
        android:appCategory="productivity"
        android:dataExtractionRules="@xml/data_rules"
        android:fullBackupContent="@xml/backup_content"
        android:icon="@mipmap/notallyx"
        android:label="@string/app_name"
        android:localeConfig="@xml/locales"
        android:roundIcon="@mipmap/notallyx_round"
        android:theme="@style/AppTheme">

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">

            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />

        </provider>

        <activity
            android:name=".presentation.activity.main.MainActivity"
            android:exported="true">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <meta-data
                android:name="android.app.shortcuts"
                android:resource="@xml/shortcuts" />

        </activity>

        <activity
            android:name=".presentation.activity.note.EditListActivity"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".presentation.activity.note.EditNoteActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustResize">

            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="*/*" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.SEND_MULTIPLE" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="*/*" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="file" android:mimeType="text/*" />
                <data android:scheme="content" android:mimeType="text/*" />

                <data android:scheme="file" android:mimeType="application/json" />
                <data android:scheme="content" android:mimeType="application/json" />

                <data android:scheme="file" android:mimeType="application/xml" />
                <data android:scheme="content" android:mimeType="application/xml" />
            </intent-filter>
        </activity>

        <activity android:name=".presentation.activity.note.ViewImageActivity" />

        <activity android:name=".presentation.activity.note.SelectLabelsActivity" />

        <activity android:name=".presentation.activity.note.reminders.RemindersActivity" />

        <activity
            android:name=".presentation.activity.note.RecordAudioActivity"
            android:launchMode="singleTask" />

        <activity android:name=".presentation.activity.note.PlayAudioActivity" />

        <activity
            android:name=".presentation.activity.ConfigureWidgetActivity"
            android:exported="false">

            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_CONFIGURE" />
            </intent-filter>

        </activity>

        <activity
            android:name=".presentation.activity.note.PickNoteActivity"
            android:exported="false">
        </activity>

        <activity
          android:name=".utils.ErrorActivity"
          android:exported="true"
          android:label="@string/unknown_error"
          android:process=":error_activity">
            <intent-filter>
            <action android:name="cat.ereza.customactivityoncrash.ERROR" />
            </intent-filter>
        </activity>

        <receiver
            android:name=".presentation.widget.WidgetProvider"
            android:exported="false"
            android:label="@string/single_note_or_list">

            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/widget" />

        </receiver>

        <receiver android:name=".presentation.activity.note.reminders.ReminderReceiver" android:enabled="true" android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.app.action.SCHEDULE_EXACT_ALARM_PERMISSION_STATE_CHANGED" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <service
            android:name=".presentation.widget.WidgetService"
            android:permission="android.permission.BIND_REMOTEVIEWS" />

        <service
            android:name=".utils.audio.AudioRecordService"
            android:exported="false"
            android:foregroundServiceType="microphone" />

        <service
            android:name=".utils.audio.AudioPlayService"
            android:exported="false"
            android:foregroundServiceType="mediaPlayback" />

    </application>

</manifest>