package com.rodrigmatrix.weatheryou.data.mapper

import com.rodrigmatrix.weatheryou.data.local.model.WeatherLocationEntity
import com.rodrigmatrix.weatheryou.domain.model.WeatherLocation

class WeatherLocationDomainToEntityMapper {

    fun map(source: WeatherLocation): WeatherLocationEntity {
        return WeatherLocationEntity(
            id = source.id,
            latitude = source.latitude,
            longitude = source.longitude,
            name = source.name,
            countryCode = source.countryCode,
            timeZone = source.timeZone,
            orderIndex = source.orderIndex,
        )
    }
}