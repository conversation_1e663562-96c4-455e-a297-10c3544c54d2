<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission android:name="android.hardware.location.gps" />

    <uses-feature android:name="android.hardware.type.watch" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@android:style/Theme.DeviceDefault"
        android:name="com.rodrigmatrix.weatheryou.wearos.WeatherYouWearOsApp">
        <uses-library
            android:name="com.google.android.wearable"
            android:required="true" />
        <meta-data
            android:name="com.google.android.wearable.standalone"
            android:value="true" />

        <activity
            android:name="com.rodrigmatrix.weatheryou.wearos.presentation.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.AppTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Complications -->
        <service android:name=".complications.DayWeatherComplicationProviderService"
            android:exported="true"
            android:icon="@drawable/ic_sunny"
            android:label="@string/day_weather_complication_label"
            android:permission="com.google.android.wearable.permission.BIND_COMPLICATION_PROVIDER">
            <intent-filter>
                <action android:name="android.support.wearable.complications.ACTION_COMPLICATION_UPDATE_REQUEST" />
            </intent-filter>
            <meta-data
                android:name="android.support.wearable.complications.UPDATE_PERIOD_SECONDS"
                android:value="0"/>

            <meta-data
                android:name="android.support.wearable.complications.SUPPORTED_TYPES"
                android:value="ICON,SMALL_IMAGE, RANGED_VALUE" />
        </service>
    </application>

</manifest>