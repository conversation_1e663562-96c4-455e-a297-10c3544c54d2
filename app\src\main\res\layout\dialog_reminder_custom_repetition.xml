<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:padding="16dp">
  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.google.android.material.textfield.TextInputLayout
      style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
      android:layout_width="match_parent"
      android:layout_height="wrap_content">
      <com.google.android.material.textfield.TextInputEditText
        android:id="@+id/Value"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="@string/repetition_value_hint"
        android:inputType="number" />
    </com.google.android.material.textfield.TextInputLayout>

    <RadioGroup
      android:id="@+id/TimeUnitGroup"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_marginTop="16dp">

      <RadioButton
        android:id="@+id/Minutes"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:checked="true"
        android:text="@string/minutes" />

      <RadioButton
        android:id="@+id/Hours"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/hours" />

      <RadioButton
        android:id="@+id/Days"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/days" />

      <RadioButton
        android:id="@+id/Weeks"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/weeks" />

      <RadioButton
        android:id="@+id/Months"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/months" />

      <RadioButton
        android:id="@+id/Years"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/years" />
    </RadioGroup>

  </LinearLayout>
</ScrollView>
