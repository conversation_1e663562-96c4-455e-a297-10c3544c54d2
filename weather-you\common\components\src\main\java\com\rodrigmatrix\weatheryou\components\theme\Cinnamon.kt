package com.rodrigmatrix.weatheryou.components.theme

import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.ui.graphics.Color

private val primaryLight = Color(0xFF6E5D00)
private val onPrimaryLight = Color(0xFFFFFFFF)
private val primaryContainerLight = Color(0xFFFFE368)
private val onPrimaryContainerLight = Color(0xFF554800)
private val secondaryLight = Color(0xFF6B5E24)
private val onSecondaryLight = Color(0xFFFFFFFF)
private val secondaryContainerLight = Color(0xFFF6E39C)
private val onSecondaryContainerLight = Color(0xFF544810)
private val tertiaryLight = Color(0xFF4D6700)
private val onTertiaryLight = Color(0xFFFFFFFF)
private val tertiaryContainerLight = Color(0xFFCBF26F)
private val onTertiaryContainerLight = Color(0xFF3B5000)
private val errorLight = Color(0xFFBA1A1A)
private val onErrorLight = Color(0xFFFFFFFF)
private val errorContainerLight = Color(0xFFFFDAD6)
private val onErrorContainerLight = Color(0xFF410002)
private val backgroundLight = Color(0xFFFFF9EE)
private val onBackgroundLight = Color(0xFF1E1B11)
private val surfaceLight = Color(0xFFFFF9EE)
private val onSurfaceLight = Color(0xFF1E1B11)
private val surfaceVariantLight = Color(0xFFEBE2C8)
private val onSurfaceVariantLight = Color(0xFF4C4733)
private val outlineLight = Color(0xFF7D7761)
private val outlineVariantLight = Color(0xFFCFC6AD)
private val scrimLight = Color(0xFF000000)
private val inverseSurfaceLight = Color(0xFF343025)
private val inverseOnSurfaceLight = Color(0xFFF8F0DF)
private val inversePrimaryLight = Color(0xFFE5C524)
private val surfaceDimLight = Color(0xFFE0D9C9)
private val surfaceBrightLight = Color(0xFFFFF9EE)
private val surfaceContainerLowestLight = Color(0xFFFFFFFF)
private val surfaceContainerLowLight = Color(0xFFFBF3E2)
private val surfaceContainerLight = Color(0xFFF5EDDD)
private val surfaceContainerHighLight = Color(0xFFEFE8D7)
private val surfaceContainerHighestLight = Color(0xFFE9E2D1)
private val primaryLightMediumContrast = Color(0xFF4F4200)
private val onPrimaryLightMediumContrast = Color(0xFFFFFFFF)
private val primaryContainerLightMediumContrast = Color(0xFF887400)
private val onPrimaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val secondaryLightMediumContrast = Color(0xFF4E420A)
private val onSecondaryLightMediumContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightMediumContrast = Color(0xFF827438)
private val onSecondaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryLightMediumContrast = Color(0xFF364900)
private val onTertiaryLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightMediumContrast = Color(0xFF5F7F00)
private val onTertiaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val errorLightMediumContrast = Color(0xFF8C0009)
private val onErrorLightMediumContrast = Color(0xFFFFFFFF)
private val errorContainerLightMediumContrast = Color(0xFFDA342E)
private val onErrorContainerLightMediumContrast = Color(0xFFFFFFFF)
private val backgroundLightMediumContrast = Color(0xFFFFF9EE)
private val onBackgroundLightMediumContrast = Color(0xFF1E1B11)
private val surfaceLightMediumContrast = Color(0xFFFFF9EE)
private val onSurfaceLightMediumContrast = Color(0xFF1E1B11)
private val surfaceVariantLightMediumContrast = Color(0xFFEBE2C8)
private val onSurfaceVariantLightMediumContrast = Color(0xFF484330)
private val outlineLightMediumContrast = Color(0xFF655F4A)
private val outlineVariantLightMediumContrast = Color(0xFF817A64)
private val scrimLightMediumContrast = Color(0xFF000000)
private val inverseSurfaceLightMediumContrast = Color(0xFF343025)
private val inverseOnSurfaceLightMediumContrast = Color(0xFFF8F0DF)
private val inversePrimaryLightMediumContrast = Color(0xFFE5C524)
private val surfaceDimLightMediumContrast = Color(0xFFE0D9C9)
private val surfaceBrightLightMediumContrast = Color(0xFFFFF9EE)
private val surfaceContainerLowestLightMediumContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightMediumContrast = Color(0xFFFBF3E2)
private val surfaceContainerLightMediumContrast = Color(0xFFF5EDDD)
private val surfaceContainerHighLightMediumContrast = Color(0xFFEFE8D7)
private val surfaceContainerHighestLightMediumContrast = Color(0xFFE9E2D1)
private val primaryLightHighContrast = Color(0xFF292200)
private val onPrimaryLightHighContrast = Color(0xFFFFFFFF)
private val primaryContainerLightHighContrast = Color(0xFF4F4200)
private val onPrimaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val secondaryLightHighContrast = Color(0xFF292200)
private val onSecondaryLightHighContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightHighContrast = Color(0xFF4E420A)
private val onSecondaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryLightHighContrast = Color(0xFF1A2600)
private val onTertiaryLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightHighContrast = Color(0xFF364900)
private val onTertiaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val errorLightHighContrast = Color(0xFF4E0002)
private val onErrorLightHighContrast = Color(0xFFFFFFFF)
private val errorContainerLightHighContrast = Color(0xFF8C0009)
private val onErrorContainerLightHighContrast = Color(0xFFFFFFFF)
private val backgroundLightHighContrast = Color(0xFFFFF9EE)
private val onBackgroundLightHighContrast = Color(0xFF1E1B11)
private val surfaceLightHighContrast = Color(0xFFFFF9EE)
private val onSurfaceLightHighContrast = Color(0xFF000000)
private val surfaceVariantLightHighContrast = Color(0xFFEBE2C8)
private val onSurfaceVariantLightHighContrast = Color(0xFF282413)
private val outlineLightHighContrast = Color(0xFF484330)
private val outlineVariantLightHighContrast = Color(0xFF484330)
private val scrimLightHighContrast = Color(0xFF000000)
private val inverseSurfaceLightHighContrast = Color(0xFF343025)
private val inverseOnSurfaceLightHighContrast = Color(0xFFFFFFFF)
private val inversePrimaryLightHighContrast = Color(0xFFFFECA2)
private val surfaceDimLightHighContrast = Color(0xFFE0D9C9)
private val surfaceBrightLightHighContrast = Color(0xFFFFF9EE)
private val surfaceContainerLowestLightHighContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightHighContrast = Color(0xFFFBF3E2)
private val surfaceContainerLightHighContrast = Color(0xFFF5EDDD)
private val surfaceContainerHighLightHighContrast = Color(0xFFEFE8D7)
private val surfaceContainerHighestLightHighContrast = Color(0xFFE9E2D1)
private val primaryDark = Color(0xFFFFFFFF)
private val onPrimaryDark = Color(0xFF3A3000)
private val primaryContainerDark = Color(0xFFF3D334)
private val onPrimaryContainerDark = Color(0xFF4B3F00)
private val secondaryDark = Color(0xFFD8C682)
private val onSecondaryDark = Color(0xFF3A3000)
private val secondaryContainerDark = Color(0xFF4A3F06)
private val onSecondaryContainerDark = Color(0xFFE6D48E)
private val tertiaryDark = Color(0xFFFFFFFF)
private val onTertiaryDark = Color(0xFF263500)
private val tertiaryContainerDark = Color(0xFFBCE262)
private val onTertiaryContainerDark = Color(0xFF334500)
private val errorDark = Color(0xFFFFB4AB)
private val onErrorDark = Color(0xFF690005)
private val errorContainerDark = Color(0xFF93000A)
private val onErrorContainerDark = Color(0xFFFFDAD6)
private val backgroundDark = Color(0xFF16130A)
private val onBackgroundDark = Color(0xFFE9E2D1)
private val surfaceDark = Color(0xFF16130A)
private val onSurfaceDark = Color(0xFFE9E2D1)
private val surfaceVariantDark = Color(0xFF4C4733)
private val onSurfaceVariantDark = Color(0xFFCFC6AD)
private val outlineDark = Color(0xFF989079)
private val outlineVariantDark = Color(0xFF4C4733)
private val scrimDark = Color(0xFF000000)
private val inverseSurfaceDark = Color(0xFFE9E2D1)
private val inverseOnSurfaceDark = Color(0xFF343025)
private val inversePrimaryDark = Color(0xFF6E5D00)
private val surfaceDimDark = Color(0xFF16130A)
private val surfaceBrightDark = Color(0xFF3D392E)
private val surfaceContainerLowestDark = Color(0xFF100E06)
private val surfaceContainerLowDark = Color(0xFF1E1B11)
private val surfaceContainerDark = Color(0xFF222015)
private val surfaceContainerHighDark = Color(0xFF2D2A1F)
private val surfaceContainerHighestDark = Color(0xFF383529)
private val primaryDarkMediumContrast = Color(0xFFFFFFFF)
private val onPrimaryDarkMediumContrast = Color(0xFF3A3000)
private val primaryContainerDarkMediumContrast = Color(0xFFF3D334)
private val onPrimaryContainerDarkMediumContrast = Color(0xFF261F00)
private val secondaryDarkMediumContrast = Color(0xFFDCCB85)
private val onSecondaryDarkMediumContrast = Color(0xFF1C1600)
private val secondaryContainerDarkMediumContrast = Color(0xFFA09051)
private val onSecondaryContainerDarkMediumContrast = Color(0xFF000000)
private val tertiaryDarkMediumContrast = Color(0xFFFFFFFF)
private val onTertiaryDarkMediumContrast = Color(0xFF263500)
private val tertiaryContainerDarkMediumContrast = Color(0xFFBCE262)
private val onTertiaryContainerDarkMediumContrast = Color(0xFF182300)
private val errorDarkMediumContrast = Color(0xFFFFBAB1)
private val onErrorDarkMediumContrast = Color(0xFF370001)
private val errorContainerDarkMediumContrast = Color(0xFFFF5449)
private val onErrorContainerDarkMediumContrast = Color(0xFF000000)
private val backgroundDarkMediumContrast = Color(0xFF16130A)
private val onBackgroundDarkMediumContrast = Color(0xFFE9E2D1)
private val surfaceDarkMediumContrast = Color(0xFF16130A)
private val onSurfaceDarkMediumContrast = Color(0xFFFFFAF5)
private val surfaceVariantDarkMediumContrast = Color(0xFF4C4733)
private val onSurfaceVariantDarkMediumContrast = Color(0xFFD3CAB1)
private val outlineDarkMediumContrast = Color(0xFFAAA38B)
private val outlineVariantDarkMediumContrast = Color(0xFF8A836C)
private val scrimDarkMediumContrast = Color(0xFF000000)
private val inverseSurfaceDarkMediumContrast = Color(0xFFE9E2D1)
private val inverseOnSurfaceDarkMediumContrast = Color(0xFF2D2A1F)
private val inversePrimaryDarkMediumContrast = Color(0xFF554700)
private val surfaceDimDarkMediumContrast = Color(0xFF16130A)
private val surfaceBrightDarkMediumContrast = Color(0xFF3D392E)
private val surfaceContainerLowestDarkMediumContrast = Color(0xFF100E06)
private val surfaceContainerLowDarkMediumContrast = Color(0xFF1E1B11)
private val surfaceContainerDarkMediumContrast = Color(0xFF222015)
private val surfaceContainerHighDarkMediumContrast = Color(0xFF2D2A1F)
private val surfaceContainerHighestDarkMediumContrast = Color(0xFF383529)
private val primaryDarkHighContrast = Color(0xFFFFFFFF)
private val onPrimaryDarkHighContrast = Color(0xFF000000)
private val primaryContainerDarkHighContrast = Color(0xFFF3D334)
private val onPrimaryContainerDarkHighContrast = Color(0xFF000000)
private val secondaryDarkHighContrast = Color(0xFFFFFAF5)
private val onSecondaryDarkHighContrast = Color(0xFF000000)
private val secondaryContainerDarkHighContrast = Color(0xFFDCCB85)
private val onSecondaryContainerDarkHighContrast = Color(0xFF000000)
private val tertiaryDarkHighContrast = Color(0xFFFFFFFF)
private val onTertiaryDarkHighContrast = Color(0xFF000000)
private val tertiaryContainerDarkHighContrast = Color(0xFFBCE262)
private val onTertiaryContainerDarkHighContrast = Color(0xFF000000)
private val errorDarkHighContrast = Color(0xFFFFF9F9)
private val onErrorDarkHighContrast = Color(0xFF000000)
private val errorContainerDarkHighContrast = Color(0xFFFFBAB1)
private val onErrorContainerDarkHighContrast = Color(0xFF000000)
private val backgroundDarkHighContrast = Color(0xFF16130A)
private val onBackgroundDarkHighContrast = Color(0xFFE9E2D1)
private val surfaceDarkHighContrast = Color(0xFF16130A)
private val onSurfaceDarkHighContrast = Color(0xFFFFFFFF)
private val surfaceVariantDarkHighContrast = Color(0xFF4C4733)
private val onSurfaceVariantDarkHighContrast = Color(0xFFFFFAF5)
private val outlineDarkHighContrast = Color(0xFFD3CAB1)
private val outlineVariantDarkHighContrast = Color(0xFFD3CAB1)
private val scrimDarkHighContrast = Color(0xFF000000)
private val inverseSurfaceDarkHighContrast = Color(0xFFE9E2D1)
private val inverseOnSurfaceDarkHighContrast = Color(0xFF000000)
private val inversePrimaryDarkHighContrast = Color(0xFF322A00)
private val surfaceDimDarkHighContrast = Color(0xFF16130A)
private val surfaceBrightDarkHighContrast = Color(0xFF3D392E)
private val surfaceContainerLowestDarkHighContrast = Color(0xFF100E06)
private val surfaceContainerLowDarkHighContrast = Color(0xFF1E1B11)
private val surfaceContainerDarkHighContrast = Color(0xFF222015)
private val surfaceContainerHighDarkHighContrast = Color(0xFF2D2A1F)
private val surfaceContainerHighestDarkHighContrast = Color(0xFF383529)

val cinnamonLightScheme = lightColorScheme(
    primary = primaryLight,
    onPrimary = onPrimaryLight,
    primaryContainer = primaryContainerLight,
    onPrimaryContainer = onPrimaryContainerLight,
    secondary = secondaryLight,
    onSecondary = onSecondaryLight,
    secondaryContainer = secondaryContainerLight,
    onSecondaryContainer = onSecondaryContainerLight,
    tertiary = tertiaryLight,
    onTertiary = onTertiaryLight,
    tertiaryContainer = tertiaryContainerLight,
    onTertiaryContainer = onTertiaryContainerLight,
    error = errorLight,
    onError = onErrorLight,
    errorContainer = errorContainerLight,
    onErrorContainer = onErrorContainerLight,
    background = backgroundLight,
    onBackground = onBackgroundLight,
    surface = surfaceLight,
    onSurface = onSurfaceLight,
    surfaceVariant = surfaceVariantLight,
    onSurfaceVariant = onSurfaceVariantLight,
    outline = outlineLight,
    outlineVariant = outlineVariantLight,
    scrim = scrimLight,
    inverseSurface = inverseSurfaceLight,
    inverseOnSurface = inverseOnSurfaceLight,
    inversePrimary = inversePrimaryLight,
    surfaceDim = surfaceDimLight,
    surfaceBright = surfaceBrightLight,
    surfaceContainerLowest = surfaceContainerLowestLight,
    surfaceContainerLow = surfaceContainerLowLight,
    surfaceContainer = surfaceContainerLight,
    surfaceContainerHigh = surfaceContainerHighLight,
    surfaceContainerHighest = surfaceContainerHighestLight,
)

val cinnamonDarkScheme = darkColorScheme(
    primary = primaryDark,
    onPrimary = onPrimaryDark,
    primaryContainer = primaryContainerDark,
    onPrimaryContainer = onPrimaryContainerDark,
    secondary = secondaryDark,
    onSecondary = onSecondaryDark,
    secondaryContainer = secondaryContainerDark,
    onSecondaryContainer = onSecondaryContainerDark,
    tertiary = tertiaryDark,
    onTertiary = onTertiaryDark,
    tertiaryContainer = tertiaryContainerDark,
    onTertiaryContainer = onTertiaryContainerDark,
    error = errorDark,
    onError = onErrorDark,
    errorContainer = errorContainerDark,
    onErrorContainer = onErrorContainerDark,
    background = backgroundDark,
    onBackground = onBackgroundDark,
    surface = surfaceDark,
    onSurface = onSurfaceDark,
    surfaceVariant = surfaceVariantDark,
    onSurfaceVariant = onSurfaceVariantDark,
    outline = outlineDark,
    outlineVariant = outlineVariantDark,
    scrim = scrimDark,
    inverseSurface = inverseSurfaceDark,
    inverseOnSurface = inverseOnSurfaceDark,
    inversePrimary = inversePrimaryDark,
    surfaceDim = surfaceDimDark,
    surfaceBright = surfaceBrightDark,
    surfaceContainerLowest = surfaceContainerLowestDark,
    surfaceContainerLow = surfaceContainerLowDark,
    surfaceContainer = surfaceContainerDark,
    surfaceContainerHigh = surfaceContainerHighDark,
    surfaceContainerHighest = surfaceContainerHighestDark,
)
