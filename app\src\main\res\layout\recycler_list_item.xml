<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    >

    <cn.leaqi.drawer.SwipeDrawer
      android:id="@+id/SwipeLayout"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      app:leftMode="drawer"
      app:leftType="view"
      app:leftLayout="@+id/IndentSpace"
      >

        <Space
            android:id="@+id/IndentSpace"
            android:layout_width="20dp"
            android:layout_height="match_parent"
            android:insetTop="0dp"
            android:insetBottom="0dp"
            android:minHeight="0dp"
            android:gravity="center"/>

        <RelativeLayout
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:paddingStart="0dp"
          android:paddingEnd="0dp"
          android:id="@+id/Content"
          android:gravity="center_vertical"
          >

            <ImageButton
              android:id="@+id/DragHandle"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:layout_centerVertical="true"
              android:layout_alignParentStart="true"
              android:background="?attr/selectableItemBackground"
              android:contentDescription="@string/drag_handle"
              android:padding="8dp"
              app:srcCompat="@drawable/drag_handle"
              app:tint="?attr/colorControlNormal" />

            <com.google.android.material.checkbox.MaterialCheckBox
              android:id="@+id/CheckBox"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:layout_centerVertical="true"
              android:layout_toEndOf="@id/DragHandle" />

            <com.philkes.notallyx.presentation.view.misc.EditTextAutoClearFocus
              android:id="@+id/EditText"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:minHeight="48dp"
              android:layout_centerVertical="true"
              android:layout_toEndOf="@id/CheckBox"
              android:layout_alignParentEnd="true"
              android:background="@null"
              android:hint="@string/item"
              android:imeOptions="actionNext"
              android:inputType="textMultiLine"
              android:layout_marginEnd="48dp"
              android:textAppearance="?attr/textAppearanceBodyLarge" />
        </RelativeLayout>
    </cn.leaqi.drawer.SwipeDrawer>

    <ImageButton
        android:id="@+id/Delete"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:background="?attr/selectableItemBackground"
        android:contentDescription="@string/delete"
        android:visibility="invisible"
        android:minWidth="48dp"
        android:minHeight="48dp"
        app:srcCompat="@drawable/delete"
        app:tint="?attr/colorControlNormal" />

</RelativeLayout>