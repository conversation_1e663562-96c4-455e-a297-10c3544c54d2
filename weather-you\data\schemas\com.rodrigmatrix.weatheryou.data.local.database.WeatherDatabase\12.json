{"formatVersion": 1, "database": {"version": 12, "identityHash": "7d7e8f0a2af3e372ba698aa81bea8264", "entities": [{"tableName": "locations", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `latitude` REAL NOT NULL, `orderIndex` INTEGER NOT NULL, `longitude` REAL NOT NULL, `name` TEXT NOT NULL, `countryCode` TEXT NOT NULL, `timeZone` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "latitude", "columnName": "latitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "orderIndex", "columnName": "orderIndex", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "longitude", "columnName": "longitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "countryCode", "columnName": "countryCode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timeZone", "columnName": "timeZone", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "weatherWidgetLocationCache", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `latitude` REAL NOT NULL, `longitude` REAL NOT NULL, `isCurrentLocation` INTEGER NOT NULL, `name` TEXT NOT NULL, `countryCode` TEXT NOT NULL, `timeZone` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "latitude", "columnName": "latitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "longitude", "columnName": "longitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "isCurrentLocation", "columnName": "isCurrentLocation", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "countryCode", "columnName": "countryCode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timeZone", "columnName": "timeZone", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "weather", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`name` TEXT NOT NULL, `latitude` REAL NOT NULL, `longitude` REAL NOT NULL, `isCurrentLocation` INTEGER NOT NULL, `currentWeather` REAL NOT NULL, `currentCondition` TEXT NOT NULL, `maxTemperature` REAL NOT NULL, `lowestTemperature` REAL NOT NULL, `feelsLike` REAL NOT NULL, `currentTime` TEXT NOT NULL, `isDaylight` INTEGER NOT NULL, `expirationDate` TEXT NOT NULL, `timeZone` TEXT NOT NULL, `precipitationProbability` REAL NOT NULL, `precipitationType` TEXT NOT NULL, `humidity` REAL NOT NULL, `dewPoint` REAL NOT NULL, `windSpeed` REAL NOT NULL, `windDirection` REAL NOT NULL, `uvIndex` REAL NOT NULL, `sunrise` TEXT NOT NULL, `sunset` TEXT NOT NULL, `visibility` REAL NOT NULL, `pressure` REAL NOT NULL, `countryCode` TEXT NOT NULL, `minWeekTemperature` REAL NOT NULL, `maxWeekTemperature` REAL NOT NULL, `cloudCover` REAL NOT NULL, `days` TEXT NOT NULL, `hours` TEXT NOT NULL, PRIMARY KEY(`latitude`, `longitude`))", "fields": [{"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "latitude", "columnName": "latitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "longitude", "columnName": "longitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "isCurrentLocation", "columnName": "isCurrentLocation", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "current<PERSON><PERSON><PERSON>", "columnName": "current<PERSON><PERSON><PERSON>", "affinity": "REAL", "notNull": true}, {"fieldPath": "currentCondition", "columnName": "currentCondition", "affinity": "TEXT", "notNull": true}, {"fieldPath": "maxTemperature", "columnName": "maxTemperature", "affinity": "REAL", "notNull": true}, {"fieldPath": "lowestTemperature", "columnName": "lowestTemperature", "affinity": "REAL", "notNull": true}, {"fieldPath": "feelsLike", "columnName": "feelsLike", "affinity": "REAL", "notNull": true}, {"fieldPath": "currentTime", "columnName": "currentTime", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isDaylight", "columnName": "isDaylight", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "expirationDate", "columnName": "expirationDate", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timeZone", "columnName": "timeZone", "affinity": "TEXT", "notNull": true}, {"fieldPath": "precipitationProbability", "columnName": "precipitationProbability", "affinity": "REAL", "notNull": true}, {"fieldPath": "precipitationType", "columnName": "precipitationType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "humidity", "columnName": "humidity", "affinity": "REAL", "notNull": true}, {"fieldPath": "dewPoint", "columnName": "dewPoint", "affinity": "REAL", "notNull": true}, {"fieldPath": "windSpeed", "columnName": "windSpeed", "affinity": "REAL", "notNull": true}, {"fieldPath": "windDirection", "columnName": "windDirection", "affinity": "REAL", "notNull": true}, {"fieldPath": "uvIndex", "columnName": "uvIndex", "affinity": "REAL", "notNull": true}, {"fieldPath": "sunrise", "columnName": "sunrise", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sunset", "columnName": "sunset", "affinity": "TEXT", "notNull": true}, {"fieldPath": "visibility", "columnName": "visibility", "affinity": "REAL", "notNull": true}, {"fieldPath": "pressure", "columnName": "pressure", "affinity": "REAL", "notNull": true}, {"fieldPath": "countryCode", "columnName": "countryCode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "minWeekTemperature", "columnName": "minWeekTemperature", "affinity": "REAL", "notNull": true}, {"fieldPath": "maxWeekTemperature", "columnName": "maxWeekTemperature", "affinity": "REAL", "notNull": true}, {"fieldPath": "cloudCover", "columnName": "cloudCover", "affinity": "REAL", "notNull": true}, {"fieldPath": "days", "columnName": "days", "affinity": "TEXT", "notNull": true}, {"fieldPath": "hours", "columnName": "hours", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["latitude", "longitude"]}, "indices": [], "foreignKeys": []}, {"tableName": "currentLocation", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`latitude` REAL NOT NULL, `longitude` REAL NOT NULL, `name` TEXT NOT NULL, `countryCode` TEXT NOT NULL, `timeZone` TEXT NOT NULL, `lastUpdate` TEXT NOT NULL, `id` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "latitude", "columnName": "latitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "longitude", "columnName": "longitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "countryCode", "columnName": "countryCode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timeZone", "columnName": "timeZone", "affinity": "TEXT", "notNull": true}, {"fieldPath": "lastUpdate", "columnName": "lastUpdate", "affinity": "TEXT", "notNull": true}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '7d7e8f0a2af3e372ba698aa81bea8264')"]}}