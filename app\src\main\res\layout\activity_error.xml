<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:gravity="center">

  <ScrollView
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:ignore="UselessParent">

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:gravity="center"
      android:orientation="vertical"
      android:paddingBottom="@dimen/customactivityoncrash_activity_vertical_margin"
      android:paddingLeft="@dimen/customactivityoncrash_activity_horizontal_margin"
      android:paddingRight="@dimen/customactivityoncrash_activity_horizontal_margin"
      android:paddingTop="@dimen/customactivityoncrash_activity_vertical_margin">

      <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@null"
        android:minWidth="100dp"
        android:minHeight="100dp"
        android:src="@drawable/error" />

      <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/customactivityoncrash_activity_vertical_margin"
        android:gravity="center"
        android:text="@string/crash_message"
        android:textSize="18sp"
        android:textStyle="bold" />

      <TextView
        android:id="@+id/Exception"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/customactivityoncrash_activity_vertical_margin"
        android:gravity="center"
        android:textSize="14sp" />

      <Button
        android:id="@+id/RestartButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/customactivityoncrash_activity_vertical_margin"
        android:text="@string/restart_app" />

      <com.google.android.material.button.MaterialButton
        android:id="@+id/ReportButton"
        style="@style/Widget.Material3.Button.OutlinedButton"
        app:strokeColor="?attr/colorPrimary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/report_crash"
        android:textColor="?colorPrimary" />
    </LinearLayout>
  </ScrollView>
</RelativeLayout>