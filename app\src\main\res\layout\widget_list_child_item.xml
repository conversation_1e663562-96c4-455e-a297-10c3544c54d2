<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  android:id="@+id/LinearLayout"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:paddingTop="4dp"
  android:paddingBottom="4dp"
  android:paddingStart="12dp"
  android:paddingEnd="16dp"
  android:orientation="horizontal"
  android:theme="@style/AppTheme">

  <ImageView
    android:id="@+id/CheckBox"
    android:layout_width="24dp"
    android:layout_height="24dp"
    android:layout_marginStart="18dp"
    android:layout_gravity="center_vertical"
    android:src="@drawable/checkbox" />

  <TextView
    android:id="@+id/CheckBoxText"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginTop="1dp"
    android:layout_marginStart="8dp"
    android:textAppearance="?attr/textAppearanceBodyMedium"
    android:gravity="center_vertical" />
</LinearLayout>
