package com.rodrigmatrix.weatheryou.domain.model

import org.joda.time.DateTime

data class WeatherLocation(
    val id: Int,
    val widgetId: String = "",
    val orderIndex: Int = 0,
    val name: String,
    val latitude: Double,
    val longitude: Double,
    val isCurrentLocation: Boolean,
    val currentWeather: Double,
    val currentCondition: WeatherCondition,
    val maxTemperature: Double,
    val lowestTemperature: Double,
    val feelsLike: Double,
    val currentTime: DateTime,
    val isDaylight: Boolean = false,
    val expirationDate: DateTime,
    val cloudCover: Double,
    val timeZone: String,
    val precipitationProbability: Double,
    val precipitationType: PrecipitationType,
    val humidity: Double,
    val dewPoint: Double,
    val windSpeed: Double,
    val windDirection: Double,
    val uvIndex: Double,
    val sunrise: DateTime,
    val sunset: DateTime,
    val visibility: Double,
    val pressure: Double,
    val days: List<WeatherDay> = emptyList(),
    val hours: List<WeatherHour> = emptyList(),
    val countryCode: String,
    val minWeekTemperature: Double,
    val maxWeekTemperature: Double,
)