object LocalModules {

    const val app = ":app"
    const val domain = ":domain"
    const val data = ":data"
    const val core = ":core"

    // features
    const val home = ":features:home"
    const val addLocation = ":features:addlocation"
    const val about = ":features:about"
    const val settings = ":features:settings"
    const val locationDetails = ":features:locationdetails"
    const val widgets = ":features:widgets"
    const val ads = ":ads"

    // common
    const val components = ":common:components"
    const val weatherIcons = ":common:weathericons"
}