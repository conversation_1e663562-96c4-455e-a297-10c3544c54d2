<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="8dp"
    android:paddingStart="26dp"
    android:paddingEnd="26dp"
    >

    <RadioGroup
        android:id="@+id/NotesSortByRadioGroup"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/sort_direction"
        android:textAppearance="?attr/textAppearanceSubtitle2"
        android:textSize="16sp"
        android:paddingBottom="8dp"
        android:layout_marginTop="12dp"
        />

    <RadioGroup
        android:id="@+id/NotesSortDirectionRadioGroup"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical" />

</LinearLayout>