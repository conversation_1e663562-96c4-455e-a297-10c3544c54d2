package com.rodrigmatrix.weatheryou.tv.presentation.theme

import androidx.compose.ui.graphics.Color
import androidx.tv.material3.darkColorScheme
import androidx.tv.material3.lightColorScheme

private val primaryLight = Color(0xFF543DA6)
private val onPrimaryLight = Color(0xFFFFFFFF)
private val primaryContainerLight = Color(0xFF7964CE)
private val onPrimaryContainerLight = Color(0xFFFFFFFF)
private val secondaryLight = Color(0xFF615983)
private val onSecondaryLight = Color(0xFFFFFFFF)
private val secondaryContainerLight = Color(0xFFDED4FF)
private val onSecondaryContainerLight = Color(0xFF453D65)
private val tertiaryLight = Color(0xFF872C6E)
private val onTertiaryLight = Color(0xFFFFFFFF)
private val tertiaryContainerLight = Color(0xFFB35195)
private val onTertiaryContainerLight = Color(0xFFFFFFFF)
private val errorLight = Color(0xFFBA1A1A)
private val onErrorLight = Color(0xFFFFFFFF)
private val errorContainerLight = Color(0xFFFFDAD6)
private val onErrorContainerLight = Color(0xFF410002)
private val backgroundLight = Color(0xFFFDF8FF)
private val onBackgroundLight = Color(0xFF1C1B21)
private val surfaceLight = Color(0xFFFDF8FF)
private val onSurfaceLight = Color(0xFF1C1B21)
private val surfaceVariantLight = Color(0xFFE6E0F0)
private val onSurfaceVariantLight = Color(0xFF484552)
private val outlineLight = Color(0xFF797583)
private val outlineVariantLight = Color(0xFFCAC4D4)
private val scrimLight = Color(0xFF000000)
private val inverseSurfaceLight = Color(0xFF312F37)
private val inverseOnSurfaceLight = Color(0xFFF4EFF9)
private val inversePrimaryLight = Color(0xFFCBBEFF)
private val surfaceDimLight = Color(0xFFDDD8E2)
private val surfaceBrightLight = Color(0xFFFDF8FF)
private val surfaceContainerLowestLight = Color(0xFFFFFFFF)
private val surfaceContainerLowLight = Color(0xFFF7F2FB)
private val surfaceContainerLight = Color(0xFFF1ECF6)
private val surfaceContainerHighLight = Color(0xFFECE6F0)
private val surfaceContainerHighestLight = Color(0xFFE6E0EA)
private val primaryLightMediumContrast = Color(0xFF462E98)
private val onPrimaryLightMediumContrast = Color(0xFFFFFFFF)
private val primaryContainerLightMediumContrast = Color(0xFF7964CE)
private val onPrimaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val secondaryLightMediumContrast = Color(0xFF453D65)
private val onSecondaryLightMediumContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightMediumContrast = Color(0xFF776F9A)
private val onSecondaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryLightMediumContrast = Color(0xFF761D60)
private val onTertiaryLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightMediumContrast = Color(0xFFB35195)
private val onTertiaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val errorLightMediumContrast = Color(0xFF8C0009)
private val onErrorLightMediumContrast = Color(0xFFFFFFFF)
private val errorContainerLightMediumContrast = Color(0xFFDA342E)
private val onErrorContainerLightMediumContrast = Color(0xFFFFFFFF)
private val backgroundLightMediumContrast = Color(0xFFFDF8FF)
private val onBackgroundLightMediumContrast = Color(0xFF1C1B21)
private val surfaceLightMediumContrast = Color(0xFFFDF8FF)
private val onSurfaceLightMediumContrast = Color(0xFF1C1B21)
private val surfaceVariantLightMediumContrast = Color(0xFFE6E0F0)
private val onSurfaceVariantLightMediumContrast = Color(0xFF44414E)
private val outlineLightMediumContrast = Color(0xFF615D6B)
private val outlineVariantLightMediumContrast = Color(0xFF7D7987)
private val scrimLightMediumContrast = Color(0xFF000000)
private val inverseSurfaceLightMediumContrast = Color(0xFF312F37)
private val inverseOnSurfaceLightMediumContrast = Color(0xFFF4EFF9)
private val inversePrimaryLightMediumContrast = Color(0xFFCBBEFF)
private val surfaceDimLightMediumContrast = Color(0xFFDDD8E2)
private val surfaceBrightLightMediumContrast = Color(0xFFFDF8FF)
private val surfaceContainerLowestLightMediumContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightMediumContrast = Color(0xFFF7F2FB)
private val surfaceContainerLightMediumContrast = Color(0xFFF1ECF6)
private val surfaceContainerHighLightMediumContrast = Color(0xFFECE6F0)
private val surfaceContainerHighestLightMediumContrast = Color(0xFFE6E0EA)
private val primaryLightHighContrast = Color(0xFF240073)
private val onPrimaryLightHighContrast = Color(0xFFFFFFFF)
private val primaryContainerLightHighContrast = Color(0xFF462E98)
private val onPrimaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val secondaryLightHighContrast = Color(0xFF241C42)
private val onSecondaryLightHighContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightHighContrast = Color(0xFF453D65)
private val onSecondaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryLightHighContrast = Color(0xFF470038)
private val onTertiaryLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightHighContrast = Color(0xFF761D60)
private val onTertiaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val errorLightHighContrast = Color(0xFF4E0002)
private val onErrorLightHighContrast = Color(0xFFFFFFFF)
private val errorContainerLightHighContrast = Color(0xFF8C0009)
private val onErrorContainerLightHighContrast = Color(0xFFFFFFFF)
private val backgroundLightHighContrast = Color(0xFFFDF8FF)
private val onBackgroundLightHighContrast = Color(0xFF1C1B21)
private val surfaceLightHighContrast = Color(0xFFFDF8FF)
private val onSurfaceLightHighContrast = Color(0xFF000000)
private val surfaceVariantLightHighContrast = Color(0xFFE6E0F0)
private val onSurfaceVariantLightHighContrast = Color(0xFF25222E)
private val outlineLightHighContrast = Color(0xFF44414E)
private val outlineVariantLightHighContrast = Color(0xFF44414E)
private val scrimLightHighContrast = Color(0xFF000000)
private val inverseSurfaceLightHighContrast = Color(0xFF312F37)
private val inverseOnSurfaceLightHighContrast = Color(0xFFFFFFFF)
private val inversePrimaryLightHighContrast = Color(0xFFF0E9FF)
private val surfaceDimLightHighContrast = Color(0xFFDDD8E2)
private val surfaceBrightLightHighContrast = Color(0xFFFDF8FF)
private val surfaceContainerLowestLightHighContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightHighContrast = Color(0xFFF7F2FB)
private val surfaceContainerLightHighContrast = Color(0xFFF1ECF6)
private val surfaceContainerHighLightHighContrast = Color(0xFFECE6F0)
private val surfaceContainerHighestLightHighContrast = Color(0xFFE6E0EA)
private val primaryDark = Color(0xFFCBBEFF)
private val onPrimaryDark = Color(0xFF331685)
private val primaryContainerDark = Color(0xFF7762CC)
private val onPrimaryContainerDark = Color(0xFFFFFFFF)
private val secondaryDark = Color(0xFFCBC0F1)
private val onSecondaryDark = Color(0xFF322B52)
private val secondaryContainerDark = Color(0xFF3F3860)
private val onSecondaryContainerDark = Color(0xFFD5CBFC)
private val tertiaryDark = Color(0xFFFFADE0)
private val onTertiaryDark = Color(0xFF5F024C)
private val tertiaryContainerDark = Color(0xFFB15093)
private val onTertiaryContainerDark = Color(0xFFFFFFFF)
private val errorDark = Color(0xFFFFB4AB)
private val onErrorDark = Color(0xFF690005)
private val errorContainerDark = Color(0xFF93000A)
private val onErrorContainerDark = Color(0xFFFFDAD6)
private val backgroundDark = Color(0xFF141219)
private val onBackgroundDark = Color(0xFFE6E0EA)
private val surfaceDark = Color(0xFF141219)
private val onSurfaceDark = Color(0xFFE6E0EA)
private val surfaceVariantDark = Color(0xFF484552)
private val onSurfaceVariantDark = Color(0xFFCAC4D4)
private val outlineDark = Color(0xFF938E9D)
private val outlineVariantDark = Color(0xFF484552)
private val scrimDark = Color(0xFF000000)
private val inverseSurfaceDark = Color(0xFFE6E0EA)
private val inverseOnSurfaceDark = Color(0xFF312F37)
private val inversePrimaryDark = Color(0xFF624DB6)
private val surfaceDimDark = Color(0xFF141219)
private val surfaceBrightDark = Color(0xFF3A383F)
private val surfaceContainerLowestDark = Color(0xFF0F0D14)
private val surfaceContainerLowDark = Color(0xFF1C1B21)
private val surfaceContainerDark = Color(0xFF201F25)
private val surfaceContainerHighDark = Color(0xFF2B2930)
private val surfaceContainerHighestDark = Color(0xFF36343B)
private val primaryDarkMediumContrast = Color(0xFFCFC3FF)
private val onPrimaryDarkMediumContrast = Color(0xFF180053)
private val primaryContainerDarkMediumContrast = Color(0xFF9680ED)
private val onPrimaryContainerDarkMediumContrast = Color(0xFF000000)
private val secondaryDarkMediumContrast = Color(0xFFCFC4F5)
private val onSecondaryDarkMediumContrast = Color(0xFF181036)
private val secondaryContainerDarkMediumContrast = Color(0xFF948BB8)
private val onSecondaryContainerDarkMediumContrast = Color(0xFF000000)
private val tertiaryDarkMediumContrast = Color(0xFFFFB4E1)
private val onTertiaryDarkMediumContrast = Color(0xFF320026)
private val tertiaryContainerDarkMediumContrast = Color(0xFFD46DB2)
private val onTertiaryContainerDarkMediumContrast = Color(0xFF000000)
private val errorDarkMediumContrast = Color(0xFFFFBAB1)
private val onErrorDarkMediumContrast = Color(0xFF370001)
private val errorContainerDarkMediumContrast = Color(0xFFFF5449)
private val onErrorContainerDarkMediumContrast = Color(0xFF000000)
private val backgroundDarkMediumContrast = Color(0xFF141219)
private val onBackgroundDarkMediumContrast = Color(0xFFE6E0EA)
private val surfaceDarkMediumContrast = Color(0xFF141219)
private val onSurfaceDarkMediumContrast = Color(0xFFFEF9FF)
private val surfaceVariantDarkMediumContrast = Color(0xFF484552)
private val onSurfaceVariantDarkMediumContrast = Color(0xFFCEC8D8)
private val outlineDarkMediumContrast = Color(0xFFA6A0B0)
private val outlineVariantDarkMediumContrast = Color(0xFF858190)
private val scrimDarkMediumContrast = Color(0xFF000000)
private val inverseSurfaceDarkMediumContrast = Color(0xFFE6E0EA)
private val inverseOnSurfaceDarkMediumContrast = Color(0xFF2B2930)
private val inversePrimaryDarkMediumContrast = Color(0xFF4B349E)
private val surfaceDimDarkMediumContrast = Color(0xFF141219)
private val surfaceBrightDarkMediumContrast = Color(0xFF3A383F)
private val surfaceContainerLowestDarkMediumContrast = Color(0xFF0F0D14)
private val surfaceContainerLowDarkMediumContrast = Color(0xFF1C1B21)
private val surfaceContainerDarkMediumContrast = Color(0xFF201F25)
private val surfaceContainerHighDarkMediumContrast = Color(0xFF2B2930)
private val surfaceContainerHighestDarkMediumContrast = Color(0xFF36343B)
private val primaryDarkHighContrast = Color(0xFFFEF9FF)
private val onPrimaryDarkHighContrast = Color(0xFF000000)
private val primaryContainerDarkHighContrast = Color(0xFFCFC3FF)
private val onPrimaryContainerDarkHighContrast = Color(0xFF000000)
private val secondaryDarkHighContrast = Color(0xFFFEF9FF)
private val onSecondaryDarkHighContrast = Color(0xFF000000)
private val secondaryContainerDarkHighContrast = Color(0xFFCFC4F5)
private val onSecondaryContainerDarkHighContrast = Color(0xFF000000)
private val tertiaryDarkHighContrast = Color(0xFFFFF9F9)
private val onTertiaryDarkHighContrast = Color(0xFF000000)
private val tertiaryContainerDarkHighContrast = Color(0xFFFFB4E1)
private val onTertiaryContainerDarkHighContrast = Color(0xFF000000)
private val errorDarkHighContrast = Color(0xFFFFF9F9)
private val onErrorDarkHighContrast = Color(0xFF000000)
private val errorContainerDarkHighContrast = Color(0xFFFFBAB1)
private val onErrorContainerDarkHighContrast = Color(0xFF000000)
private val backgroundDarkHighContrast = Color(0xFF141219)
private val onBackgroundDarkHighContrast = Color(0xFFE6E0EA)
private val surfaceDarkHighContrast = Color(0xFF141219)
private val onSurfaceDarkHighContrast = Color(0xFFFFFFFF)
private val surfaceVariantDarkHighContrast = Color(0xFF484552)
private val onSurfaceVariantDarkHighContrast = Color(0xFFFEF9FF)
private val outlineDarkHighContrast = Color(0xFFCEC8D8)
private val outlineVariantDarkHighContrast = Color(0xFFCEC8D8)
private val scrimDarkHighContrast = Color(0xFF000000)
private val inverseSurfaceDarkHighContrast = Color(0xFFE6E0EA)
private val inverseOnSurfaceDarkHighContrast = Color(0xFF000000)
private val inversePrimaryDarkHighContrast = Color(0xFF2C0A7F)
private val surfaceDimDarkHighContrast = Color(0xFF141219)
private val surfaceBrightDarkHighContrast = Color(0xFF3A383F)
private val surfaceContainerLowestDarkHighContrast = Color(0xFF0F0D14)
private val surfaceContainerLowDarkHighContrast = Color(0xFF1C1B21)
private val surfaceContainerDarkHighContrast = Color(0xFF201F25)
private val surfaceContainerHighDarkHighContrast = Color(0xFF2B2930)
private val surfaceContainerHighestDarkHighContrast = Color(0xFF36343B)

internal val gigasLightScheme = lightColorScheme(
    primary = primaryLight,
    onPrimary = onPrimaryLight,
    primaryContainer = primaryContainerLight,
    onPrimaryContainer = onPrimaryContainerLight,
    secondary = secondaryLight,
    onSecondary = onSecondaryLight,
    secondaryContainer = secondaryContainerLight,
    onSecondaryContainer = onSecondaryContainerLight,
    tertiary = tertiaryLight,
    onTertiary = onTertiaryLight,
    tertiaryContainer = tertiaryContainerLight,
    onTertiaryContainer = onTertiaryContainerLight,
    error = errorLight,
    onError = onErrorLight,
    errorContainer = errorContainerLight,
    onErrorContainer = onErrorContainerLight,
    background = backgroundLight,
    onBackground = onBackgroundLight,
    surface = surfaceLight,
    onSurface = onSurfaceLight,
    surfaceVariant = surfaceVariantLight,
    onSurfaceVariant = onSurfaceVariantLight,
    scrim = scrimLight,
    inverseSurface = inverseSurfaceLight,
    inverseOnSurface = inverseOnSurfaceLight,
    inversePrimary = inversePrimaryLight,
)

internal val gigasDarkScheme = darkColorScheme(
    primary = primaryDark,
    onPrimary = onPrimaryDark,
    primaryContainer = primaryContainerDark,
    onPrimaryContainer = onPrimaryContainerDark,
    secondary = secondaryDark,
    onSecondary = onSecondaryDark,
    secondaryContainer = secondaryContainerDark,
    onSecondaryContainer = onSecondaryContainerDark,
    tertiary = tertiaryDark,
    onTertiary = onTertiaryDark,
    tertiaryContainer = tertiaryContainerDark,
    onTertiaryContainer = onTertiaryContainerDark,
    error = errorDark,
    onError = onErrorDark,
    errorContainer = errorContainerDark,
    onErrorContainer = onErrorContainerDark,
    background = backgroundDark,
    onBackground = onBackgroundDark,
    surface = surfaceDark,
    onSurface = onSurfaceDark,
    surfaceVariant = surfaceVariantDark,
    onSurfaceVariant = onSurfaceVariantDark,
    scrim = scrimDark,
    inverseSurface = inverseSurfaceDark,
    inverseOnSurface = inverseOnSurfaceDark,
    inversePrimary = inversePrimaryDark,
)
