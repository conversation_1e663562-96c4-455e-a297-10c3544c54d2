plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
    alias(libs.plugins.compose.compiler)
}

android {
    namespace 'com.rodrigmatrix.weatheryou.core'
    compileSdk Sdk.compileSdk

    defaultConfig {
        minSdk Sdk.phoneMinSdk
        targetSdk Sdk.targetSdk

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    buildFeatures {
        compose true
    }
    composeOptions {
      kotlinCompilerExtensionVersion libs.versions.composeCompilerVersion.get()
    }
}

dependencies {
    implementation(libs.androidx.ktx)
    implementation(libs.androidx.lifecycle.viewmodel)

    implementation project(LocalModules.domain)

    implementation(libs.jodaTime)

    implementation(libs.kotlin.stdlib)
    implementation(libs.kotlin.coroutines.android)

    implementation platform(libs.compose.bom)
    implementation(libs.compose.ui)
    implementation(libs.compose.material3)
}