plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
    id 'kotlinx-serialization'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    alias(libs.plugins.compose.compiler)
}

def keyStorePropertiesFile = rootProject.file("keystore.properties")
//def keyStoreProperties = new Properties()
//keyStoreProperties.load(new FileInputStream(keyStorePropertiesFile))

android {
    compileSdk Sdk.compileSdk

    compileOptions {
        coreLibraryDesugaringEnabled true
    }

    defaultConfig {
        applicationId Sdk.applicationId
        minSdk Sdk.phoneMinSdk
        targetSdk Sdk.targetSdk
        versionCode computeVersionCode()
        versionName computeVersionName()

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
        javaCompileOptions {
            annotationProcessorOptions {
                arguments += [
                        "room.schemaLocation":"$projectDir/schemas".toString(),
                        "room.incremental":"true",
                        "room.expandProjection":"true"]
            }
        }
    }
    signingConfigs {
//        release {
//            keyAlias keyStoreProperties['releaseKeyAlias']
//            keyPassword keyStoreProperties['releaseKeyPassword']
//            storeFile file(keyStoreProperties['releaseKeyStore'])
//            storePassword keyStoreProperties['releaseStorePassword']
//        }
    }
    buildTypes {
        release {
            minifyEnabled true
//            signingConfig signingConfigs.release
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
        debug {
            minifyEnabled false
            debuggable true
            applicationIdSuffix ".debug"
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    kotlin {
        jvmToolchain(17)
    }
    kotlin.sourceSets.configureEach {
        languageSettings.optIn("kotlin.RequiresOptIn")
    }
    buildFeatures {
        compose true
        buildConfig = true
    }
    composeOptions {
       kotlinCompilerExtensionVersion libs.versions.composeCompilerVersion.get()
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
    namespace 'com.rodrigmatrix.weatheryou'
}

static def computeVersionName() {
    return String.format('%d.%d.%d', Sdk.phoneVersionMajor, Sdk.phoneVersionMinor, Sdk.phoneVersionPatch)
}

static def computeVersionCode() {
    return ((Sdk.phoneVersionMajor * 100000)
    + (Sdk.phoneVersionMinor * 10000)
    + (Sdk.phoneVersionPatch * 1000)
    + Integer.valueOf(System.getenv("CIRCLE_BUILD_NUM") ?: Sdk.localVersionCode))
}

dependencies {
    implementation project(LocalModules.domain)
    implementation project(LocalModules.data)
    implementation project(LocalModules.core)
    implementation project(LocalModules.ads)

    implementation project(LocalModules.components)
    implementation project(LocalModules.home)
    implementation project(LocalModules.addLocation)
    implementation project(LocalModules.weatherIcons)
    implementation project(LocalModules.about)
    implementation project(LocalModules.settings)
    implementation project(LocalModules.locationDetails)
    implementation project(LocalModules.widgets)
    implementation libs.androidx.splash.screen

    implementation(libs.android.material)

    implementation(libs.androidx.ktx)
    implementation(libs.androidx.lifecycle)
    implementation(libs.androidx.window)

    implementation(libs.kotlin.stdlib)
    implementation(libs.kotlin.coroutines.android)
    implementation(libs.kotlin.serialization)

    implementation platform(libs.compose.bom)
    implementation(libs.compose.ui)
    implementation(libs.compose.runtime)
    implementation(libs.compose.material3)
    implementation(libs.compose.material)
    implementation(libs.compose.preview)
    implementation(libs.compose.activity)
    implementation(libs.compose.navigation)
    implementation(libs.androidx.adaptive)
    implementation(libs.androidx.adaptive.layout)
    implementation(libs.androidx.adaptive.navigation)
    implementation(libs.androidx.adaptive.navigation.suite)
    implementation(libs.compose.constraint.layout)
    implementation(libs.compose.window.size)
    implementation(libs.accompanist.permissions)
    implementation(libs.accompanist.navigation)
    implementation(libs.accompanist.adaptive)

    implementation(libs.glanceAppWidget)
    implementation(libs.glanceAppWidget.material3)
    implementation(libs.glance.preview)
    implementation(libs.glanceAppWidget.preview)

    implementation(libs.koin.android)
    implementation(libs.koin.compose)

    implementation platform(libs.firebase.bom)
    implementation(libs.firebase.remote.config)
    implementation(libs.firebase.messaging)
    implementation(libs.firebase.inAppMessaging)
    implementation(libs.firebase.crashlytics)
    implementation(libs.firebase.analytics)

    implementation(libs.workManager)

    implementation(libs.google.play.location)
    implementation(libs.google.play.review)
    implementation(libs.google.play.ads)
    implementation(libs.google.play.billing)

    implementation(libs.jodaTime)

    implementation(libs.gson)

    implementation(libs.coil)

    implementation(libs.lottie.compose)

    coreLibraryDesugaring libs.desugar.jdk.libs

    debugImplementation(libs.composeUiTooling)
    debugImplementation(libs.composeTestManifest)

    testImplementation(libs.junit)
    androidTestImplementation(libs.testExtJunit)
    androidTestImplementation(libs.espresso.core)
    androidTestImplementation(libs.composeUiTestJunit)
}