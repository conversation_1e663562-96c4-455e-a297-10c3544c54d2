<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:layout_height="match_parent"
  android:layout_width="match_parent"
  android:fillViewport="true"
  >
  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:paddingTop="16dp"
    android:paddingBottom="0dp"
    >

    <com.skydoves.colorpickerview.ColorPickerView
      android:id="@+id/ColorPicker"
      android:layout_width="match_parent"
      android:layout_height="300dp" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="24dp"
      android:layout_marginTop="8dp"
      android:orientation="horizontal">
      <ImageButton
        android:id="@+id/Restore"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:src="@drawable/restore"
        android:background="?attr/selectableItemBackground"
        />
      <com.skydoves.colorpickerview.sliders.BrightnessSlideBar
        android:id="@+id/BrightnessSlideBar"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:layout_marginStart="8dp"/>
    </LinearLayout>

    <RelativeLayout
      android:id="@+id/rlSearch"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:gravity="center" >

      <com.skydoves.colorpickerview.AlphaTileView
        android:id="@+id/TileView"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginEnd="4dp"
        android:layout_toStartOf="@id/ColorCode"
        android:orientation="horizontal"
        android:layout_centerVertical="true"
        app:tileSize="20" />

      <EditText
        android:id="@+id/ColorCode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="FFFFFF"
        android:maxLines="1"
        android:maxLength="6"
        android:ems="6"
        android:minEms="3"
        android:maxEms="6"
        android:typeface="monospace"
        android:digits="0123456789ABCDEFabcdef"
        android:layout_toStartOf="@+id/CopyCode"
        android:layout_centerVertical="true"/>

      <ImageView
        android:id="@+id/CopyCode"
        android:layout_marginStart="2dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:src="@drawable/content_copy" />
    </RelativeLayout>

    <TextView
      android:id="@+id/ColorExistsText"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:textAppearance="@style/TextAppearance.Material3.BodySmall"
      android:text="@string/color_exists"
      android:gravity="center"
      android:visibility="invisible"
      />
    <androidx.recyclerview.widget.RecyclerView
      android:id="@+id/ExistingColors"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:clipToPadding="false"
      android:layout_marginTop="4dp"
      android:paddingStart="4dp"
      android:paddingEnd="4dp"
      android:paddingBottom="4dp"
      android:scrollIndicators="top"
      app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
      app:spanCount="6" />
  </LinearLayout>
</androidx.core.widget.NestedScrollView>