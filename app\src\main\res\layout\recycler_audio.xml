<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:paddingStart="24dp"
    android:paddingTop="12dp"
    android:paddingEnd="24dp"
    android:paddingBottom="12dp">

    <TextView
        android:id="@+id/Date"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginEnd="8dp"
        android:layout_toStartOf="@id/Length"
        android:textAppearance="?attr/textAppearanceBodyMedium" />

    <TextView
        android:id="@+id/Length"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:textAppearance="?attr/textAppearanceBodyMedium"
        android:textColor="?attr/colorControlNormal" />

</RelativeLayout>