<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:paddingBottom="8dp"
  android:paddingStart="8dp"
  android:paddingEnd="8dp"
  android:paddingTop="18dp"
  android:orientation="vertical"
  android:elevation="4dp">

  <TextView
    android:id="@+id/AddImage"
    style="@style/BottomSheetText"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:text="@string/add_images"
    app:drawableStartCompat="@drawable/add_images"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent" />

  <TextView
    android:id="@+id/AttachFile"
    style="@style/BottomSheetText"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:text="@string/attach_file"
    app:drawableStartCompat="@drawable/text_file"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent" />

  <TextView
    android:id="@+id/RecordAudio"
    style="@style/BottomSheetText"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:text="@string/record_audio"
    app:drawableStartCompat="@drawable/record_audio"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent" />

</LinearLayout>