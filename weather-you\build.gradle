// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        google()
        mavenCentral()
        mavenLocal()
    }
    dependencies {
        classpath libs.gradle
        classpath libs.kotlin.gradle.plugin
        classpath libs.jetbrains.kotlin.serialization
        classpath libs.google.services
        classpath libs.firebase.crashlytics.gradle

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

tasks.register('clean', Delete) {
    delete rootProject.buildDir
}