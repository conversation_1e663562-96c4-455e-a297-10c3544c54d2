<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#FFFFFF"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="sans-serif-medium"
        android:paddingBottom="16dp"
        android:text="<PERSON>’s albums"
        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="16dp"
        android:text="Sun 4 June 2023"
        android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
        android:textColor="#99000000" />

    <TextView
        style="@style/WidgetPreview.ListItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="6dp"
        android:text="1989" />

    <TextView
        style="@style/WidgetPreview.ListItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="6dp"
        android:text="Reputation" />

    <TextView
        style="@style/WidgetPreview.ListItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="6dp"
        android:text="Lover" />

    <TextView
        style="@style/WidgetPreview.ListItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="6dp"
        android:text="Folklore" />

    <TextView
        style="@style/WidgetPreview.ListItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="6dp"
        android:text="Evermore" />

    <TextView
        style="@style/WidgetPreview.ListItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Midnights" />

</LinearLayout>