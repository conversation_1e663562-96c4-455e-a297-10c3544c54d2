package com.rodrigmatrix.weatheryou.tv.presentation.theme

import androidx.tv.material3.lightColorScheme
import androidx.compose.ui.graphics.Color
import androidx.tv.material3.darkColorScheme

private val primaryLight = Color(0xFF8A3501)
private val onPrimaryLight = Color(0xFFFFFFFF)
private val primaryContainerLight = Color(0xFFBA5825)
private val onPrimaryContainerLight = Color(0xFFFFFFFF)
private val secondaryLight = Color(0xFF84523B)
private val onSecondaryLight = Color(0xFFFFFFFF)
private val secondaryContainerLight = Color(0xFFFFC6AE)
private val onSecondaryContainerLight = Color(0xFF5E331E)
private val tertiaryLight = Color(0xFF666000)
private val onTertiaryLight = Color(0xFFFFFFFF)
private val tertiaryContainerLight = Color(0xFFB6AE3C)
private val onTertiaryContainerLight = Color(0xFF242100)
private val errorLight = Color(0xFFBA1A1A)
private val onErrorLight = Color(0xFFFFFFFF)
private val errorContainerLight = Color(0xFFFFDAD6)
private val onErrorContainerLight = Color(0xFF410002)
private val backgroundLight = Color(0xFFFFF8F6)
private val onBackgroundLight = Color(0xFF231915)
private val surfaceLight = Color(0xFFFFF8F6)
private val onSurfaceLight = Color(0xFF231915)
private val surfaceVariantLight = Color(0xFFFADCD1)
private val onSurfaceVariantLight = Color(0xFF56423A)
private val outlineLight = Color(0xFF897269)
private val outlineVariantLight = Color(0xFFDDC1B6)
private val scrimLight = Color(0xFF000000)
private val inverseSurfaceLight = Color(0xFF392E29)
private val inverseOnSurfaceLight = Color(0xFFFFEDE6)
private val inversePrimaryLight = Color(0xFFFFB695)
private val surfaceDimLight = Color(0xFFE9D6CF)
private val surfaceBrightLight = Color(0xFFFFF8F6)
private val surfaceContainerLowestLight = Color(0xFFFFFFFF)
private val surfaceContainerLowLight = Color(0xFFFFF1EC)
private val surfaceContainerLight = Color(0xFFFEEAE2)
private val surfaceContainerHighLight = Color(0xFFF8E4DD)
private val surfaceContainerHighestLight = Color(0xFFF2DFD7)
private val primaryLightMediumContrast = Color(0xFF752C00)
private val onPrimaryLightMediumContrast = Color(0xFFFFFFFF)
private val primaryContainerLightMediumContrast = Color(0xFFBA5825)
private val onPrimaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val secondaryLightMediumContrast = Color(0xFF633722)
private val onSecondaryLightMediumContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightMediumContrast = Color(0xFF9D684F)
private val onSecondaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryLightMediumContrast = Color(0xFF484400)
private val onTertiaryLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightMediumContrast = Color(0xFF7E7700)
private val onTertiaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val errorLightMediumContrast = Color(0xFF8C0009)
private val onErrorLightMediumContrast = Color(0xFFFFFFFF)
private val errorContainerLightMediumContrast = Color(0xFFDA342E)
private val onErrorContainerLightMediumContrast = Color(0xFFFFFFFF)
private val backgroundLightMediumContrast = Color(0xFFFFF8F6)
private val onBackgroundLightMediumContrast = Color(0xFF231915)
private val surfaceLightMediumContrast = Color(0xFFFFF8F6)
private val onSurfaceLightMediumContrast = Color(0xFF231915)
private val surfaceVariantLightMediumContrast = Color(0xFFFADCD1)
private val onSurfaceVariantLightMediumContrast = Color(0xFF523F36)
private val outlineLightMediumContrast = Color(0xFF705A51)
private val outlineVariantLightMediumContrast = Color(0xFF8D766C)
private val scrimLightMediumContrast = Color(0xFF000000)
private val inverseSurfaceLightMediumContrast = Color(0xFF392E29)
private val inverseOnSurfaceLightMediumContrast = Color(0xFFFFEDE6)
private val inversePrimaryLightMediumContrast = Color(0xFFFFB695)
private val surfaceDimLightMediumContrast = Color(0xFFE9D6CF)
private val surfaceBrightLightMediumContrast = Color(0xFFFFF8F6)
private val surfaceContainerLowestLightMediumContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightMediumContrast = Color(0xFFFFF1EC)
private val surfaceContainerLightMediumContrast = Color(0xFFFEEAE2)
private val surfaceContainerHighLightMediumContrast = Color(0xFFF8E4DD)
private val surfaceContainerHighestLightMediumContrast = Color(0xFFF2DFD7)
private val primaryLightHighContrast = Color(0xFF401400)
private val onPrimaryLightHighContrast = Color(0xFFFFFFFF)
private val primaryContainerLightHighContrast = Color(0xFF752C00)
private val onPrimaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val secondaryLightHighContrast = Color(0xFF3C1806)
private val onSecondaryLightHighContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightHighContrast = Color(0xFF633722)
private val onSecondaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryLightHighContrast = Color(0xFF262300)
private val onTertiaryLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightHighContrast = Color(0xFF484400)
private val onTertiaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val errorLightHighContrast = Color(0xFF4E0002)
private val onErrorLightHighContrast = Color(0xFFFFFFFF)
private val errorContainerLightHighContrast = Color(0xFF8C0009)
private val onErrorContainerLightHighContrast = Color(0xFFFFFFFF)
private val backgroundLightHighContrast = Color(0xFFFFF8F6)
private val onBackgroundLightHighContrast = Color(0xFF231915)
private val surfaceLightHighContrast = Color(0xFFFFF8F6)
private val onSurfaceLightHighContrast = Color(0xFF000000)
private val surfaceVariantLightHighContrast = Color(0xFFFADCD1)
private val onSurfaceVariantLightHighContrast = Color(0xFF302019)
private val outlineLightHighContrast = Color(0xFF523F36)
private val outlineVariantLightHighContrast = Color(0xFF523F36)
private val scrimLightHighContrast = Color(0xFF000000)
private val inverseSurfaceLightHighContrast = Color(0xFF392E29)
private val inverseOnSurfaceLightHighContrast = Color(0xFFFFFFFF)
private val inversePrimaryLightHighContrast = Color(0xFFFFE7DE)
private val surfaceDimLightHighContrast = Color(0xFFE9D6CF)
private val surfaceBrightLightHighContrast = Color(0xFFFFF8F6)
private val surfaceContainerLowestLightHighContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightHighContrast = Color(0xFFFFF1EC)
private val surfaceContainerLightHighContrast = Color(0xFFFEEAE2)
private val surfaceContainerHighLightHighContrast = Color(0xFFF8E4DD)
private val surfaceContainerHighestLightHighContrast = Color(0xFFF2DFD7)
private val primaryDark = Color(0xFFFFB695)
private val onPrimaryDark = Color(0xFF571E00)
private val primaryContainerDark = Color(0xFFB25220)
private val onPrimaryContainerDark = Color(0xFFFFFFFF)
private val secondaryDark = Color(0xFFF9B89B)
private val onSecondaryDark = Color(0xFF4D2612)
private val secondaryContainerDark = Color(0xFF5D321E)
private val onSecondaryContainerDark = Color(0xFFFFC4AA)
private val tertiaryDark = Color(0xFFE1D861)
private val onTertiaryDark = Color(0xFF353200)
private val tertiaryContainerDark = Color(0xFFB6AE3C)
private val onTertiaryContainerDark = Color(0xFF232100)
private val errorDark = Color(0xFFFFB4AB)
private val onErrorDark = Color(0xFF690005)
private val errorContainerDark = Color(0xFF93000A)
private val onErrorContainerDark = Color(0xFFFFDAD6)
private val backgroundDark = Color(0xFF1A110D)
private val onBackgroundDark = Color(0xFFF2DFD7)
private val surfaceDark = Color(0xFF1A110D)
private val onSurfaceDark = Color(0xFFF2DFD7)
private val surfaceVariantDark = Color(0xFF56423A)
private val onSurfaceVariantDark = Color(0xFFDDC1B6)
private val outlineDark = Color(0xFFA48B81)
private val outlineVariantDark = Color(0xFF56423A)
private val scrimDark = Color(0xFF000000)
private val inverseSurfaceDark = Color(0xFFF2DFD7)
private val inverseOnSurfaceDark = Color(0xFF392E29)
private val inversePrimaryDark = Color(0xFF9D4310)
private val surfaceDimDark = Color(0xFF1A110D)
private val surfaceBrightDark = Color(0xFF423732)
private val surfaceContainerLowestDark = Color(0xFF150C08)
private val surfaceContainerLowDark = Color(0xFF231915)
private val surfaceContainerDark = Color(0xFF271D19)
private val surfaceContainerHighDark = Color(0xFF322823)
private val surfaceContainerHighestDark = Color(0xFF3E322D)
private val primaryDarkMediumContrast = Color(0xFFFFBB9E)
private val onPrimaryDarkMediumContrast = Color(0xFF2D0C00)
private val primaryContainerDarkMediumContrast = Color(0xFFDE733E)
private val onPrimaryContainerDarkMediumContrast = Color(0xFF000000)
private val secondaryDarkMediumContrast = Color(0xFFFEBC9F)
private val onSecondaryDarkMediumContrast = Color(0xFF2C0C00)
private val secondaryContainerDarkMediumContrast = Color(0xFFBD8369)
private val onSecondaryContainerDarkMediumContrast = Color(0xFF000000)
private val tertiaryDarkMediumContrast = Color(0xFFE1D861)
private val onTertiaryDarkMediumContrast = Color(0xFF222000)
private val tertiaryContainerDarkMediumContrast = Color(0xFFB6AE3C)
private val onTertiaryContainerDarkMediumContrast = Color(0xFF000000)
private val errorDarkMediumContrast = Color(0xFFFFBAB1)
private val onErrorDarkMediumContrast = Color(0xFF370001)
private val errorContainerDarkMediumContrast = Color(0xFFFF5449)
private val onErrorContainerDarkMediumContrast = Color(0xFF000000)
private val backgroundDarkMediumContrast = Color(0xFF1A110D)
private val onBackgroundDarkMediumContrast = Color(0xFFF2DFD7)
private val surfaceDarkMediumContrast = Color(0xFF1A110D)
private val onSurfaceDarkMediumContrast = Color(0xFFFFF9F8)
private val surfaceVariantDarkMediumContrast = Color(0xFF56423A)
private val onSurfaceVariantDarkMediumContrast = Color(0xFFE1C5BA)
private val outlineDarkMediumContrast = Color(0xFFB79D93)
private val outlineVariantDarkMediumContrast = Color(0xFF967E74)
private val scrimDarkMediumContrast = Color(0xFF000000)
private val inverseSurfaceDarkMediumContrast = Color(0xFFF2DFD7)
private val inverseOnSurfaceDarkMediumContrast = Color(0xFF332823)
private val inversePrimaryDarkMediumContrast = Color(0xFF7D2F00)
private val surfaceDimDarkMediumContrast = Color(0xFF1A110D)
private val surfaceBrightDarkMediumContrast = Color(0xFF423732)
private val surfaceContainerLowestDarkMediumContrast = Color(0xFF150C08)
private val surfaceContainerLowDarkMediumContrast = Color(0xFF231915)
private val surfaceContainerDarkMediumContrast = Color(0xFF271D19)
private val surfaceContainerHighDarkMediumContrast = Color(0xFF322823)
private val surfaceContainerHighestDarkMediumContrast = Color(0xFF3E322D)
private val primaryDarkHighContrast = Color(0xFFFFF9F8)
private val onPrimaryDarkHighContrast = Color(0xFF000000)
private val primaryContainerDarkHighContrast = Color(0xFFFFBB9E)
private val onPrimaryContainerDarkHighContrast = Color(0xFF000000)
private val secondaryDarkHighContrast = Color(0xFFFFF9F8)
private val onSecondaryDarkHighContrast = Color(0xFF000000)
private val secondaryContainerDarkHighContrast = Color(0xFFFEBC9F)
private val onSecondaryContainerDarkHighContrast = Color(0xFF000000)
private val tertiaryDarkHighContrast = Color(0xFFFFFAEF)
private val onTertiaryDarkHighContrast = Color(0xFF000000)
private val tertiaryContainerDarkHighContrast = Color(0xFFD7CE59)
private val onTertiaryContainerDarkHighContrast = Color(0xFF000000)
private val errorDarkHighContrast = Color(0xFFFFF9F9)
private val onErrorDarkHighContrast = Color(0xFF000000)
private val errorContainerDarkHighContrast = Color(0xFFFFBAB1)
private val onErrorContainerDarkHighContrast = Color(0xFF000000)
private val backgroundDarkHighContrast = Color(0xFF1A110D)
private val onBackgroundDarkHighContrast = Color(0xFFF2DFD7)
private val surfaceDarkHighContrast = Color(0xFF1A110D)
private val onSurfaceDarkHighContrast = Color(0xFFFFFFFF)
private val surfaceVariantDarkHighContrast = Color(0xFF56423A)
private val onSurfaceVariantDarkHighContrast = Color(0xFFFFF9F8)
private val outlineDarkHighContrast = Color(0xFFE1C5BA)
private val outlineVariantDarkHighContrast = Color(0xFFE1C5BA)
private val scrimDarkHighContrast = Color(0xFF000000)
private val inverseSurfaceDarkHighContrast = Color(0xFFF2DFD7)
private val inverseOnSurfaceDarkHighContrast = Color(0xFF000000)
private val inversePrimaryDarkHighContrast = Color(0xFF4D1A00)
private val surfaceDimDarkHighContrast = Color(0xFF1A110D)
private val surfaceBrightDarkHighContrast = Color(0xFF423732)
private val surfaceContainerLowestDarkHighContrast = Color(0xFF150C08)
private val surfaceContainerLowDarkHighContrast = Color(0xFF231915)
private val surfaceContainerDarkHighContrast = Color(0xFF271D19)
private val surfaceContainerHighDarkHighContrast = Color(0xFF322823)
private val surfaceContainerHighestDarkHighContrast = Color(0xFF3E322D)

internal val peruTanLightScheme = lightColorScheme(
    primary = primaryLight,
    onPrimary = onPrimaryLight,
    primaryContainer = primaryContainerLight,
    onPrimaryContainer = onPrimaryContainerLight,
    secondary = secondaryLight,
    onSecondary = onSecondaryLight,
    secondaryContainer = secondaryContainerLight,
    onSecondaryContainer = onSecondaryContainerLight,
    tertiary = tertiaryLight,
    onTertiary = onTertiaryLight,
    tertiaryContainer = tertiaryContainerLight,
    onTertiaryContainer = onTertiaryContainerLight,
    error = errorLight,
    onError = onErrorLight,
    errorContainer = errorContainerLight,
    onErrorContainer = onErrorContainerLight,
    background = backgroundLight,
    onBackground = onBackgroundLight,
    surface = surfaceLight,
    onSurface = onSurfaceLight,
    surfaceVariant = surfaceVariantLight,
    onSurfaceVariant = onSurfaceVariantLight,
    scrim = scrimLight,
    inverseSurface = inverseSurfaceLight,
    inverseOnSurface = inverseOnSurfaceLight,
    inversePrimary = inversePrimaryLight,
)

internal val peruTanDarkScheme = darkColorScheme(
    primary = primaryDark,
    onPrimary = onPrimaryDark,
    primaryContainer = primaryContainerDark,
    onPrimaryContainer = onPrimaryContainerDark,
    secondary = secondaryDark,
    onSecondary = onSecondaryDark,
    secondaryContainer = secondaryContainerDark,
    onSecondaryContainer = onSecondaryContainerDark,
    tertiary = tertiaryDark,
    onTertiary = onTertiaryDark,
    tertiaryContainer = tertiaryContainerDark,
    onTertiaryContainer = onTertiaryContainerDark,
    error = errorDark,
    onError = onErrorDark,
    errorContainer = errorContainerDark,
    onErrorContainer = onErrorContainerDark,
    background = backgroundDark,
    onBackground = onBackgroundDark,
    surface = surfaceDark,
    onSurface = onSurfaceDark,
    surfaceVariant = surfaceVariantDark,
    onSurfaceVariant = onSurfaceVariantDark,
    scrim = scrimDark,
    inverseSurface = inverseSurfaceDark,
    inverseOnSurface = inverseOnSurfaceDark,
    inversePrimary = inversePrimaryDark,
)

