package com.rodrigmatrix.weatheryou.components.theme

import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.ui.graphics.Color

private val primaryLight = Color(0xFF006875)
private val onPrimaryLight = Color(0xFFFFFFFF)
private val primaryContainerLight = Color(0xFF78EBFF)
private val onPrimaryContainerLight = Color(0xFF004B54)
private val secondaryLight = Color(0xFF38656D)
private val onSecondaryLight = Color(0xFFFFFFFF)
private val secondaryContainerLight = Color(0xFFBDECF5)
private val onSecondaryContainerLight = Color(0xFF204F57)
private val tertiaryLight = Color(0xFF695682)
private val onTertiaryLight = Color(0xFFFFFFFF)
private val tertiaryContainerLight = Color(0xFFE7D3FF)
private val onTertiaryContainerLight = Color(0xFF4D3B65)
private val errorLight = Color(0xFFBA1A1A)
private val onErrorLight = Color(0xFFFFFFFF)
private val errorContainerLight = Color(0xFFFFDAD6)
private val onErrorContainerLight = Color(0xFF410002)
private val backgroundLight = Color(0xFFF5FAFC)
private val onBackgroundLight = Color(0xFF161D1E)
private val surfaceLight = Color(0xFFF5FAFC)
private val onSurfaceLight = Color(0xFF161D1E)
private val surfaceVariantLight = Color(0xFFD7E5E8)
private val onSurfaceVariantLight = Color(0xFF3C494B)
private val outlineLight = Color(0xFF6C797C)
private val outlineVariantLight = Color(0xFFBBC9CC)
private val scrimLight = Color(0xFF000000)
private val inverseSurfaceLight = Color(0xFF2B3133)
private val inverseOnSurfaceLight = Color(0xFFECF2F3)
private val inversePrimaryLight = Color(0xFF4BD8EE)
private val surfaceDimLight = Color(0xFFD5DBDC)
private val surfaceBrightLight = Color(0xFFF5FAFC)
private val surfaceContainerLowestLight = Color(0xFFFFFFFF)
private val surfaceContainerLowLight = Color(0xFFEFF5F6)
private val surfaceContainerLight = Color(0xFFE9EFF0)
private val surfaceContainerHighLight = Color(0xFFE3E9EA)
private val surfaceContainerHighestLight = Color(0xFFDEE3E5)
private val primaryLightMediumContrast = Color(0xFF004A54)
private val onPrimaryLightMediumContrast = Color(0xFFFFFFFF)
private val primaryContainerLightMediumContrast = Color(0xFF008190)
private val onPrimaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val secondaryLightMediumContrast = Color(0xFF194951)
private val onSecondaryLightMediumContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightMediumContrast = Color(0xFF4F7B84)
private val onSecondaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryLightMediumContrast = Color(0xFF4C3B64)
private val onTertiaryLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightMediumContrast = Color(0xFF7F6C99)
private val onTertiaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val errorLightMediumContrast = Color(0xFF8C0009)
private val onErrorLightMediumContrast = Color(0xFFFFFFFF)
private val errorContainerLightMediumContrast = Color(0xFFDA342E)
private val onErrorContainerLightMediumContrast = Color(0xFFFFFFFF)
private val backgroundLightMediumContrast = Color(0xFFF5FAFC)
private val onBackgroundLightMediumContrast = Color(0xFF161D1E)
private val surfaceLightMediumContrast = Color(0xFFF5FAFC)
private val onSurfaceLightMediumContrast = Color(0xFF161D1E)
private val surfaceVariantLightMediumContrast = Color(0xFFD7E5E8)
private val onSurfaceVariantLightMediumContrast = Color(0xFF384547)
private val outlineLightMediumContrast = Color(0xFF546164)
private val outlineVariantLightMediumContrast = Color(0xFF707D80)
private val scrimLightMediumContrast = Color(0xFF000000)
private val inverseSurfaceLightMediumContrast = Color(0xFF2B3133)
private val inverseOnSurfaceLightMediumContrast = Color(0xFFECF2F3)
private val inversePrimaryLightMediumContrast = Color(0xFF4BD8EE)
private val surfaceDimLightMediumContrast = Color(0xFFD5DBDC)
private val surfaceBrightLightMediumContrast = Color(0xFFF5FAFC)
private val surfaceContainerLowestLightMediumContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightMediumContrast = Color(0xFFEFF5F6)
private val surfaceContainerLightMediumContrast = Color(0xFFE9EFF0)
private val surfaceContainerHighLightMediumContrast = Color(0xFFE3E9EA)
private val surfaceContainerHighestLightMediumContrast = Color(0xFFDEE3E5)
private val primaryLightHighContrast = Color(0xFF00272C)
private val onPrimaryLightHighContrast = Color(0xFFFFFFFF)
private val primaryContainerLightHighContrast = Color(0xFF004A54)
private val onPrimaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val secondaryLightHighContrast = Color(0xFF00272C)
private val onSecondaryLightHighContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightHighContrast = Color(0xFF194951)
private val onSecondaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryLightHighContrast = Color(0xFF2A1A41)
private val onTertiaryLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightHighContrast = Color(0xFF4C3B64)
private val onTertiaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val errorLightHighContrast = Color(0xFF4E0002)
private val onErrorLightHighContrast = Color(0xFFFFFFFF)
private val errorContainerLightHighContrast = Color(0xFF8C0009)
private val onErrorContainerLightHighContrast = Color(0xFFFFFFFF)
private val backgroundLightHighContrast = Color(0xFFF5FAFC)
private val onBackgroundLightHighContrast = Color(0xFF161D1E)
private val surfaceLightHighContrast = Color(0xFFF5FAFC)
private val onSurfaceLightHighContrast = Color(0xFF000000)
private val surfaceVariantLightHighContrast = Color(0xFFD7E5E8)
private val onSurfaceVariantLightHighContrast = Color(0xFF1A2628)
private val outlineLightHighContrast = Color(0xFF384547)
private val outlineVariantLightHighContrast = Color(0xFF384547)
private val scrimLightHighContrast = Color(0xFF000000)
private val inverseSurfaceLightHighContrast = Color(0xFF2B3133)
private val inverseOnSurfaceLightHighContrast = Color(0xFFFFFFFF)
private val inversePrimaryLightHighContrast = Color(0xFFC1F5FF)
private val surfaceDimLightHighContrast = Color(0xFFD5DBDC)
private val surfaceBrightLightHighContrast = Color(0xFFF5FAFC)
private val surfaceContainerLowestLightHighContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightHighContrast = Color(0xFFEFF5F6)
private val surfaceContainerLightHighContrast = Color(0xFFE9EFF0)
private val surfaceContainerHighLightHighContrast = Color(0xFFE3E9EA)
private val surfaceContainerHighestLightHighContrast = Color(0xFFDEE3E5)
private val primaryDark = Color(0xFFF4FDFF)
private val onPrimaryDark = Color(0xFF00363D)
private val primaryContainerDark = Color(0xFF58E3F8)
private val onPrimaryContainerDark = Color(0xFF00444D)
private val secondaryDark = Color(0xFFA0CED7)
private val onSecondaryDark = Color(0xFF00363D)
private val secondaryContainerDark = Color(0xFF11434B)
private val onSecondaryContainerDark = Color(0xFFAAD8E2)
private val tertiaryDark = Color(0xFFFFFBFF)
private val onTertiaryDark = Color(0xFF392851)
private val tertiaryContainerDark = Color(0xFFDFC8FB)
private val onTertiaryContainerDark = Color(0xFF47355F)
private val errorDark = Color(0xFFFFB4AB)
private val onErrorDark = Color(0xFF690005)
private val errorContainerDark = Color(0xFF93000A)
private val onErrorContainerDark = Color(0xFFFFDAD6)
private val backgroundDark = Color(0xFF0E1416)
private val onBackgroundDark = Color(0xFFDEE3E5)
private val surfaceDark = Color(0xFF0E1416)
private val onSurfaceDark = Color(0xFFDEE3E5)
private val surfaceVariantDark = Color(0xFF3C494B)
private val onSurfaceVariantDark = Color(0xFFBBC9CC)
private val outlineDark = Color(0xFF869396)
private val outlineVariantDark = Color(0xFF3C494B)
private val scrimDark = Color(0xFF000000)
private val inverseSurfaceDark = Color(0xFFDEE3E5)
private val inverseOnSurfaceDark = Color(0xFF2B3133)
private val inversePrimaryDark = Color(0xFF006875)
private val surfaceDimDark = Color(0xFF0E1416)
private val surfaceBrightDark = Color(0xFF343A3C)
private val surfaceContainerLowestDark = Color(0xFF090F10)
private val surfaceContainerLowDark = Color(0xFF161D1E)
private val surfaceContainerDark = Color(0xFF1A2122)
private val surfaceContainerHighDark = Color(0xFF252B2C)
private val surfaceContainerHighestDark = Color(0xFF303637)
private val primaryDarkMediumContrast = Color(0xFFF4FDFF)
private val onPrimaryDarkMediumContrast = Color(0xFF00363D)
private val primaryContainerDarkMediumContrast = Color(0xFF58E3F8)
private val onPrimaryContainerDarkMediumContrast = Color(0xFF002025)
private val secondaryDarkMediumContrast = Color(0xFFA4D3DC)
private val onSecondaryDarkMediumContrast = Color(0xFF001A1E)
private val secondaryContainerDarkMediumContrast = Color(0xFF6B98A1)
private val onSecondaryContainerDarkMediumContrast = Color(0xFF000000)
private val tertiaryDarkMediumContrast = Color(0xFFFFFBFF)
private val onTertiaryDarkMediumContrast = Color(0xFF392851)
private val tertiaryContainerDarkMediumContrast = Color(0xFFDFC8FB)
private val onTertiaryContainerDarkMediumContrast = Color(0xFF25143C)
private val errorDarkMediumContrast = Color(0xFFFFBAB1)
private val onErrorDarkMediumContrast = Color(0xFF370001)
private val errorContainerDarkMediumContrast = Color(0xFFFF5449)
private val onErrorContainerDarkMediumContrast = Color(0xFF000000)
private val backgroundDarkMediumContrast = Color(0xFF0E1416)
private val onBackgroundDarkMediumContrast = Color(0xFFDEE3E5)
private val surfaceDarkMediumContrast = Color(0xFF0E1416)
private val onSurfaceDarkMediumContrast = Color(0xFFF6FCFD)
private val surfaceVariantDarkMediumContrast = Color(0xFF3C494B)
private val onSurfaceVariantDarkMediumContrast = Color(0xFFC0CDD0)
private val outlineDarkMediumContrast = Color(0xFF98A5A8)
private val outlineVariantDarkMediumContrast = Color(0xFF788588)
private val scrimDarkMediumContrast = Color(0xFF000000)
private val inverseSurfaceDarkMediumContrast = Color(0xFFDEE3E5)
private val inverseOnSurfaceDarkMediumContrast = Color(0xFF252B2C)
private val inversePrimaryDarkMediumContrast = Color(0xFF00505A)
private val surfaceDimDarkMediumContrast = Color(0xFF0E1416)
private val surfaceBrightDarkMediumContrast = Color(0xFF343A3C)
private val surfaceContainerLowestDarkMediumContrast = Color(0xFF090F10)
private val surfaceContainerLowDarkMediumContrast = Color(0xFF161D1E)
private val surfaceContainerDarkMediumContrast = Color(0xFF1A2122)
private val surfaceContainerHighDarkMediumContrast = Color(0xFF252B2C)
private val surfaceContainerHighestDarkMediumContrast = Color(0xFF303637)
private val primaryDarkHighContrast = Color(0xFFF4FDFF)
private val onPrimaryDarkHighContrast = Color(0xFF000000)
private val primaryContainerDarkHighContrast = Color(0xFF58E3F8)
private val onPrimaryContainerDarkHighContrast = Color(0xFF000000)
private val secondaryDarkHighContrast = Color(0xFFF2FDFF)
private val onSecondaryDarkHighContrast = Color(0xFF000000)
private val secondaryContainerDarkHighContrast = Color(0xFFA4D3DC)
private val onSecondaryContainerDarkHighContrast = Color(0xFF000000)
private val tertiaryDarkHighContrast = Color(0xFFFFFBFF)
private val onTertiaryDarkHighContrast = Color(0xFF000000)
private val tertiaryContainerDarkHighContrast = Color(0xFFDFC8FB)
private val onTertiaryContainerDarkHighContrast = Color(0xFF000000)
private val errorDarkHighContrast = Color(0xFFFFF9F9)
private val onErrorDarkHighContrast = Color(0xFF000000)
private val errorContainerDarkHighContrast = Color(0xFFFFBAB1)
private val onErrorContainerDarkHighContrast = Color(0xFF000000)
private val backgroundDarkHighContrast = Color(0xFF0E1416)
private val onBackgroundDarkHighContrast = Color(0xFFDEE3E5)
private val surfaceDarkHighContrast = Color(0xFF0E1416)
private val onSurfaceDarkHighContrast = Color(0xFFFFFFFF)
private val surfaceVariantDarkHighContrast = Color(0xFF3C494B)
private val onSurfaceVariantDarkHighContrast = Color(0xFFF2FDFF)
private val outlineDarkHighContrast = Color(0xFFC0CDD0)
private val outlineVariantDarkHighContrast = Color(0xFFC0CDD0)
private val scrimDarkHighContrast = Color(0xFF000000)
private val inverseSurfaceDarkHighContrast = Color(0xFFDEE3E5)
private val inverseOnSurfaceDarkHighContrast = Color(0xFF000000)
private val inversePrimaryDarkHighContrast = Color(0xFF002F36)
private val surfaceDimDarkHighContrast = Color(0xFF0E1416)
private val surfaceBrightDarkHighContrast = Color(0xFF343A3C)
private val surfaceContainerLowestDarkHighContrast = Color(0xFF090F10)
private val surfaceContainerLowDarkHighContrast = Color(0xFF161D1E)
private val surfaceContainerDarkHighContrast = Color(0xFF1A2122)
private val surfaceContainerHighDarkHighContrast = Color(0xFF252B2C)
private val surfaceContainerHighestDarkHighContrast = Color(0xFF303637)

val mosqueLightScheme = lightColorScheme(
    primary = primaryLight,
    onPrimary = onPrimaryLight,
    primaryContainer = primaryContainerLight,
    onPrimaryContainer = onPrimaryContainerLight,
    secondary = secondaryLight,
    onSecondary = onSecondaryLight,
    secondaryContainer = secondaryContainerLight,
    onSecondaryContainer = onSecondaryContainerLight,
    tertiary = tertiaryLight,
    onTertiary = onTertiaryLight,
    tertiaryContainer = tertiaryContainerLight,
    onTertiaryContainer = onTertiaryContainerLight,
    error = errorLight,
    onError = onErrorLight,
    errorContainer = errorContainerLight,
    onErrorContainer = onErrorContainerLight,
    background = backgroundLight,
    onBackground = onBackgroundLight,
    surface = surfaceLight,
    onSurface = onSurfaceLight,
    surfaceVariant = surfaceVariantLight,
    onSurfaceVariant = onSurfaceVariantLight,
    outline = outlineLight,
    outlineVariant = outlineVariantLight,
    scrim = scrimLight,
    inverseSurface = inverseSurfaceLight,
    inverseOnSurface = inverseOnSurfaceLight,
    inversePrimary = inversePrimaryLight,
    surfaceDim = surfaceDimLight,
    surfaceBright = surfaceBrightLight,
    surfaceContainerLowest = surfaceContainerLowestLight,
    surfaceContainerLow = surfaceContainerLowLight,
    surfaceContainer = surfaceContainerLight,
    surfaceContainerHigh = surfaceContainerHighLight,
    surfaceContainerHighest = surfaceContainerHighestLight,
)

val mosqueDarkScheme = darkColorScheme(
    primary = primaryDark,
    onPrimary = onPrimaryDark,
    primaryContainer = primaryContainerDark,
    onPrimaryContainer = onPrimaryContainerDark,
    secondary = secondaryDark,
    onSecondary = onSecondaryDark,
    secondaryContainer = secondaryContainerDark,
    onSecondaryContainer = onSecondaryContainerDark,
    tertiary = tertiaryDark,
    onTertiary = onTertiaryDark,
    tertiaryContainer = tertiaryContainerDark,
    onTertiaryContainer = onTertiaryContainerDark,
    error = errorDark,
    onError = onErrorDark,
    errorContainer = errorContainerDark,
    onErrorContainer = onErrorContainerDark,
    background = backgroundDark,
    onBackground = onBackgroundDark,
    surface = surfaceDark,
    onSurface = onSurfaceDark,
    surfaceVariant = surfaceVariantDark,
    onSurfaceVariant = onSurfaceVariantDark,
    outline = outlineDark,
    outlineVariant = outlineVariantDark,
    scrim = scrimDark,
    inverseSurface = inverseSurfaceDark,
    inverseOnSurface = inverseOnSurfaceDark,
    inversePrimary = inversePrimaryDark,
    surfaceDim = surfaceDimDark,
    surfaceBright = surfaceBrightDark,
    surfaceContainerLowest = surfaceContainerLowestDark,
    surfaceContainerLow = surfaceContainerLowDark,
    surfaceContainer = surfaceContainerDark,
    surfaceContainerHigh = surfaceContainerHighDark,
    surfaceContainerHighest = surfaceContainerHighestDark,
)