package io.despicable.chromeos.`data`.database

import androidx.room.InvalidationTracker
import androidx.room.RoomOpenDelegate
import androidx.room.migration.AutoMigrationSpec
import androidx.room.migration.Migration
import androidx.room.util.TableInfo
import androidx.room.util.TableInfo.Companion.read
import androidx.room.util.dropFtsSyncTriggers
import androidx.sqlite.SQLiteConnection
import androidx.sqlite.execSQL
import io.despicable.chromeos.`data`.database.dao.WeatherDao
import io.despicable.chromeos.`data`.database.dao.WeatherDao_Impl
import javax.`annotation`.processing.Generated
import kotlin.Lazy
import kotlin.String
import kotlin.Suppress
import kotlin.collections.List
import kotlin.collections.Map
import kotlin.collections.MutableList
import kotlin.collections.MutableMap
import kotlin.collections.MutableSet
import kotlin.collections.Set
import kotlin.collections.mutableListOf
import kotlin.collections.mutableMapOf
import kotlin.collections.mutableSetOf
import kotlin.reflect.KClass

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
public class WeatherDatabase_Impl : WeatherDatabase() {
  private val _weatherDao: Lazy<WeatherDao> = lazy {
    WeatherDao_Impl(this)
  }

  protected override fun createOpenDelegate(): RoomOpenDelegate {
    val _openDelegate: RoomOpenDelegate = object : RoomOpenDelegate(1,
        "bcc4c56fbf19f9f0c16df9dea5c402b3", "6e336ae995017f9a5e0ab400ff43fb95") {
      public override fun createAllTables(connection: SQLiteConnection) {
        connection.execSQL("CREATE TABLE IF NOT EXISTS `weather_data` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `cityName` TEXT NOT NULL, `temperature` INTEGER NOT NULL, `weatherCondition` TEXT NOT NULL, `humidity` INTEGER NOT NULL, `windSpeed` REAL NOT NULL, `lastUpdated` INTEGER NOT NULL)")
        connection.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)")
        connection.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'bcc4c56fbf19f9f0c16df9dea5c402b3')")
      }

      public override fun dropAllTables(connection: SQLiteConnection) {
        connection.execSQL("DROP TABLE IF EXISTS `weather_data`")
      }

      public override fun onCreate(connection: SQLiteConnection) {
      }

      public override fun onOpen(connection: SQLiteConnection) {
        internalInitInvalidationTracker(connection)
      }

      public override fun onPreMigrate(connection: SQLiteConnection) {
        dropFtsSyncTriggers(connection)
      }

      public override fun onPostMigrate(connection: SQLiteConnection) {
      }

      public override fun onValidateSchema(connection: SQLiteConnection):
          RoomOpenDelegate.ValidationResult {
        val _columnsWeatherData: MutableMap<String, TableInfo.Column> = mutableMapOf()
        _columnsWeatherData.put("id", TableInfo.Column("id", "INTEGER", true, 1, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsWeatherData.put("cityName", TableInfo.Column("cityName", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsWeatherData.put("temperature", TableInfo.Column("temperature", "INTEGER", true, 0,
            null, TableInfo.CREATED_FROM_ENTITY))
        _columnsWeatherData.put("weatherCondition", TableInfo.Column("weatherCondition", "TEXT",
            true, 0, null, TableInfo.CREATED_FROM_ENTITY))
        _columnsWeatherData.put("humidity", TableInfo.Column("humidity", "INTEGER", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsWeatherData.put("windSpeed", TableInfo.Column("windSpeed", "REAL", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsWeatherData.put("lastUpdated", TableInfo.Column("lastUpdated", "INTEGER", true, 0,
            null, TableInfo.CREATED_FROM_ENTITY))
        val _foreignKeysWeatherData: MutableSet<TableInfo.ForeignKey> = mutableSetOf()
        val _indicesWeatherData: MutableSet<TableInfo.Index> = mutableSetOf()
        val _infoWeatherData: TableInfo = TableInfo("weather_data", _columnsWeatherData,
            _foreignKeysWeatherData, _indicesWeatherData)
        val _existingWeatherData: TableInfo = read(connection, "weather_data")
        if (!_infoWeatherData.equals(_existingWeatherData)) {
          return RoomOpenDelegate.ValidationResult(false, """
              |weather_data(io.despicable.chromeos.data.database.entity.WeatherEntity).
              | Expected:
              |""".trimMargin() + _infoWeatherData + """
              |
              | Found:
              |""".trimMargin() + _existingWeatherData)
        }
        return RoomOpenDelegate.ValidationResult(true, null)
      }
    }
    return _openDelegate
  }

  protected override fun createInvalidationTracker(): InvalidationTracker {
    val _shadowTablesMap: MutableMap<String, String> = mutableMapOf()
    val _viewTables: MutableMap<String, Set<String>> = mutableMapOf()
    return InvalidationTracker(this, _shadowTablesMap, _viewTables, "weather_data")
  }

  public override fun clearAllTables() {
    super.performClear(false, "weather_data")
  }

  protected override fun getRequiredTypeConverterClasses(): Map<KClass<*>, List<KClass<*>>> {
    val _typeConvertersMap: MutableMap<KClass<*>, List<KClass<*>>> = mutableMapOf()
    _typeConvertersMap.put(WeatherDao::class, WeatherDao_Impl.getRequiredConverters())
    return _typeConvertersMap
  }

  public override fun getRequiredAutoMigrationSpecClasses(): Set<KClass<out AutoMigrationSpec>> {
    val _autoMigrationSpecsSet: MutableSet<KClass<out AutoMigrationSpec>> = mutableSetOf()
    return _autoMigrationSpecsSet
  }

  public override
      fun createAutoMigrations(autoMigrationSpecs: Map<KClass<out AutoMigrationSpec>, AutoMigrationSpec>):
      List<Migration> {
    val _autoMigrations: MutableList<Migration> = mutableListOf()
    return _autoMigrations
  }

  public override fun weatherDao(): WeatherDao = _weatherDao.value
}
