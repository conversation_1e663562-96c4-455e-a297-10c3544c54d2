<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/Toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:navigationIcon="?attr/homeAsUpIndicator" />

    <Chronometer
        android:id="@+id/Timer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/ButtonBar"
        android:layout_below="@id/Toolbar"
        android:fontFamily="monospace"
        android:gravity="center"
        android:textSize="40sp" />

    <LinearLayout
        android:id="@+id/ButtonBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="32dp"
        android:baselineAligned="false">

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/Stop"
                android:layout_height="wrap_content"
                android:layout_width="wrap_content"
                android:layout_gravity="center"
                android:text="@string/stop"
                android:textColor="@color/stop_text"
                app:backgroundTint="@color/stop_background"
                app:rippleColor="?attr/colorControlHighlight" />

        </FrameLayout>

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/Main"
                android:layout_height="wrap_content"
                android:layout_width="wrap_content"
                android:layout_gravity="center"
                android:text="@string/start" />

        </FrameLayout>

    </LinearLayout>

</RelativeLayout>