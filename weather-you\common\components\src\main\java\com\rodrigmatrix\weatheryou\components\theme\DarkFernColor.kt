package com.rodrigmatrix.weatheryou.components.theme

import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.ui.graphics.Color

private val primaryLight = Color(0xFF0E5E1A)
private val onPrimaryLight = Color(0xFFFFFFFF)
private val primaryContainerLight = Color(0xFF3B843C)
private val onPrimaryContainerLight = Color(0xFFFFFFFF)
private val secondaryLight = Color(0xFF496645)
private val onSecondaryLight = Color(0xFFFFFFFF)
private val secondaryContainerLight = Color(0xFFCEF0C6)
private val onSecondaryContainerLight = Color(0xFF355132)
private val tertiaryLight = Color(0xFF005488)
private val onTertiaryLight = Color(0xFFFFFFFF)
private val tertiaryContainerLight = Color(0xFF267ABA)
private val onTertiaryContainerLight = Color(0xFFFFFFFF)
private val errorLight = Color(0xFFBA1A1A)
private val onErrorLight = Color(0xFFFFFFFF)
private val errorContainerLight = Color(0xFFFFDAD6)
private val onErrorContainerLight = Color(0xFF410002)
private val backgroundLight = Color(0xFFF7FBF1)
private val onBackgroundLight = Color(0xFF181D17)
private val surfaceLight = Color(0xFFF7FBF1)
private val onSurfaceLight = Color(0xFF181D17)
private val surfaceVariantLight = Color(0xFFDCE5D5)
private val onSurfaceVariantLight = Color(0xFF40493E)
private val outlineLight = Color(0xFF707A6C)
private val outlineVariantLight = Color(0xFFC0C9BA)
private val scrimLight = Color(0xFF000000)
private val inverseSurfaceLight = Color(0xFF2D322B)
private val inverseOnSurfaceLight = Color(0xFFEEF2E8)
private val inversePrimaryLight = Color(0xFF8BD986)
private val surfaceDimLight = Color(0xFFD7DBD2)
private val surfaceBrightLight = Color(0xFFF7FBF1)
private val surfaceContainerLowestLight = Color(0xFFFFFFFF)
private val surfaceContainerLowLight = Color(0xFFF1F5EB)
private val surfaceContainerLight = Color(0xFFEBEFE5)
private val surfaceContainerHighLight = Color(0xFFE6EAE0)
private val surfaceContainerHighestLight = Color(0xFFE0E4DA)
private val primaryLightMediumContrast = Color(0xFF004F11)
private val onPrimaryLightMediumContrast = Color(0xFFFFFFFF)
private val primaryContainerLightMediumContrast = Color(0xFF3B843C)
private val onPrimaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val secondaryLightMediumContrast = Color(0xFF2E492B)
private val onSecondaryLightMediumContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightMediumContrast = Color(0xFF5E7C5A)
private val onSecondaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryLightMediumContrast = Color(0xFF004672)
private val onTertiaryLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightMediumContrast = Color(0xFF267ABA)
private val onTertiaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val errorLightMediumContrast = Color(0xFF8C0009)
private val onErrorLightMediumContrast = Color(0xFFFFFFFF)
private val errorContainerLightMediumContrast = Color(0xFFDA342E)
private val onErrorContainerLightMediumContrast = Color(0xFFFFFFFF)
private val backgroundLightMediumContrast = Color(0xFFF7FBF1)
private val onBackgroundLightMediumContrast = Color(0xFF181D17)
private val surfaceLightMediumContrast = Color(0xFFF7FBF1)
private val onSurfaceLightMediumContrast = Color(0xFF181D17)
private val surfaceVariantLightMediumContrast = Color(0xFFDCE5D5)
private val onSurfaceVariantLightMediumContrast = Color(0xFF3C453A)
private val outlineLightMediumContrast = Color(0xFF586255)
private val outlineVariantLightMediumContrast = Color(0xFF747D70)
private val scrimLightMediumContrast = Color(0xFF000000)
private val inverseSurfaceLightMediumContrast = Color(0xFF2D322B)
private val inverseOnSurfaceLightMediumContrast = Color(0xFFEEF2E8)
private val inversePrimaryLightMediumContrast = Color(0xFF8BD986)
private val surfaceDimLightMediumContrast = Color(0xFFD7DBD2)
private val surfaceBrightLightMediumContrast = Color(0xFFF7FBF1)
private val surfaceContainerLowestLightMediumContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightMediumContrast = Color(0xFFF1F5EB)
private val surfaceContainerLightMediumContrast = Color(0xFFEBEFE5)
private val surfaceContainerHighLightMediumContrast = Color(0xFFE6EAE0)
private val surfaceContainerHighestLightMediumContrast = Color(0xFFE0E4DA)
private val primaryLightHighContrast = Color(0xFF002905)
private val onPrimaryLightHighContrast = Color(0xFFFFFFFF)
private val primaryContainerLightHighContrast = Color(0xFF004F11)
private val onPrimaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val secondaryLightHighContrast = Color(0xFF0D280D)
private val onSecondaryLightHighContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightHighContrast = Color(0xFF2E492B)
private val onSecondaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryLightHighContrast = Color(0xFF00243E)
private val onTertiaryLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightHighContrast = Color(0xFF004672)
private val onTertiaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val errorLightHighContrast = Color(0xFF4E0002)
private val onErrorLightHighContrast = Color(0xFFFFFFFF)
private val errorContainerLightHighContrast = Color(0xFF8C0009)
private val onErrorContainerLightHighContrast = Color(0xFFFFFFFF)
private val backgroundLightHighContrast = Color(0xFFF7FBF1)
private val onBackgroundLightHighContrast = Color(0xFF181D17)
private val surfaceLightHighContrast = Color(0xFFF7FBF1)
private val onSurfaceLightHighContrast = Color(0xFF000000)
private val surfaceVariantLightHighContrast = Color(0xFFDCE5D5)
private val onSurfaceVariantLightHighContrast = Color(0xFF1E261C)
private val outlineLightHighContrast = Color(0xFF3C453A)
private val outlineVariantLightHighContrast = Color(0xFF3C453A)
private val scrimLightHighContrast = Color(0xFF000000)
private val inverseSurfaceLightHighContrast = Color(0xFF2D322B)
private val inverseOnSurfaceLightHighContrast = Color(0xFFFFFFFF)
private val inversePrimaryLightHighContrast = Color(0xFFB1FFAA)
private val surfaceDimLightHighContrast = Color(0xFFD7DBD2)
private val surfaceBrightLightHighContrast = Color(0xFFF7FBF1)
private val surfaceContainerLowestLightHighContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightHighContrast = Color(0xFFF1F5EB)
private val surfaceContainerLightHighContrast = Color(0xFFEBEFE5)
private val surfaceContainerHighLightHighContrast = Color(0xFFE6EAE0)
private val surfaceContainerHighestLightHighContrast = Color(0xFFE0E4DA)
private val primaryDark = Color(0xFF8BD986)
private val onPrimaryDark = Color(0xFF00390A)
private val primaryContainerDark = Color(0xFF1E6925)
private val onPrimaryContainerDark = Color(0xFFFFFFFF)
private val secondaryDark = Color(0xFFAFCFA8)
private val onSecondaryDark = Color(0xFF1C361A)
private val secondaryContainerDark = Color(0xFF2A4628)
private val onSecondaryContainerDark = Color(0xFFBCDDB5)
private val tertiaryDark = Color(0xFF99CBFF)
private val onTertiaryDark = Color(0xFF003355)
private val tertiaryContainerDark = Color(0xFF00609A)
private val onTertiaryContainerDark = Color(0xFFFFFFFF)
private val errorDark = Color(0xFFFFB4AB)
private val onErrorDark = Color(0xFF690005)
private val errorContainerDark = Color(0xFF93000A)
private val onErrorContainerDark = Color(0xFFFFDAD6)
private val backgroundDark = Color(0xFF10150F)
private val onBackgroundDark = Color(0xFFE0E4DA)
private val surfaceDark = Color(0xFF10150F)
private val onSurfaceDark = Color(0xFFE0E4DA)
private val surfaceVariantDark = Color(0xFF40493E)
private val onSurfaceVariantDark = Color(0xFFC0C9BA)
private val outlineDark = Color(0xFF8A9385)
private val outlineVariantDark = Color(0xFF40493E)
private val scrimDark = Color(0xFF000000)
private val inverseSurfaceDark = Color(0xFFE0E4DA)
private val inverseOnSurfaceDark = Color(0xFF2D322B)
private val inversePrimaryDark = Color(0xFF216C27)
private val surfaceDimDark = Color(0xFF10150F)
private val surfaceBrightDark = Color(0xFF363A34)
private val surfaceContainerLowestDark = Color(0xFF0B0F0A)
private val surfaceContainerLowDark = Color(0xFF181D17)
private val surfaceContainerDark = Color(0xFF1C211B)
private val surfaceContainerHighDark = Color(0xFF272B25)
private val surfaceContainerHighestDark = Color(0xFF313630)
private val primaryDarkMediumContrast = Color(0xFF90DD8A)
private val onPrimaryDarkMediumContrast = Color(0xFF001C03)
private val primaryContainerDarkMediumContrast = Color(0xFF57A155)
private val onPrimaryContainerDarkMediumContrast = Color(0xFF000000)
private val secondaryDarkMediumContrast = Color(0xFFB3D4AC)
private val onSecondaryDarkMediumContrast = Color(0xFF021B04)
private val secondaryContainerDarkMediumContrast = Color(0xFF7A9974)
private val onSecondaryContainerDarkMediumContrast = Color(0xFF000000)
private val tertiaryDarkMediumContrast = Color(0xFFA2CFFF)
private val onTertiaryDarkMediumContrast = Color(0xFF00182B)
private val tertiaryContainerDarkMediumContrast = Color(0xFF4A96D7)
private val onTertiaryContainerDarkMediumContrast = Color(0xFF000000)
private val errorDarkMediumContrast = Color(0xFFFFBAB1)
private val onErrorDarkMediumContrast = Color(0xFF370001)
private val errorContainerDarkMediumContrast = Color(0xFFFF5449)
private val onErrorContainerDarkMediumContrast = Color(0xFF000000)
private val backgroundDarkMediumContrast = Color(0xFF10150F)
private val onBackgroundDarkMediumContrast = Color(0xFFE0E4DA)
private val surfaceDarkMediumContrast = Color(0xFF10150F)
private val onSurfaceDarkMediumContrast = Color(0xFFF8FCF2)
private val surfaceVariantDarkMediumContrast = Color(0xFF40493E)
private val onSurfaceVariantDarkMediumContrast = Color(0xFFC4CEBE)
private val outlineDarkMediumContrast = Color(0xFF9CA697)
private val outlineVariantDarkMediumContrast = Color(0xFF7C8678)
private val scrimDarkMediumContrast = Color(0xFF000000)
private val inverseSurfaceDarkMediumContrast = Color(0xFFE0E4DA)
private val inverseOnSurfaceDarkMediumContrast = Color(0xFF272B25)
private val inversePrimaryDarkMediumContrast = Color(0xFF005413)
private val surfaceDimDarkMediumContrast = Color(0xFF10150F)
private val surfaceBrightDarkMediumContrast = Color(0xFF363A34)
private val surfaceContainerLowestDarkMediumContrast = Color(0xFF0B0F0A)
private val surfaceContainerLowDarkMediumContrast = Color(0xFF181D17)
private val surfaceContainerDarkMediumContrast = Color(0xFF1C211B)
private val surfaceContainerHighDarkMediumContrast = Color(0xFF272B25)
private val surfaceContainerHighestDarkMediumContrast = Color(0xFF313630)
private val primaryDarkHighContrast = Color(0xFFF1FFEA)
private val onPrimaryDarkHighContrast = Color(0xFF000000)
private val primaryContainerDarkHighContrast = Color(0xFF90DD8A)
private val onPrimaryContainerDarkHighContrast = Color(0xFF000000)
private val secondaryDarkHighContrast = Color(0xFFF1FFEA)
private val onSecondaryDarkHighContrast = Color(0xFF000000)
private val secondaryContainerDarkHighContrast = Color(0xFFB3D4AC)
private val onSecondaryContainerDarkHighContrast = Color(0xFF000000)
private val tertiaryDarkHighContrast = Color(0xFFFAFAFF)
private val onTertiaryDarkHighContrast = Color(0xFF000000)
private val tertiaryContainerDarkHighContrast = Color(0xFFA2CFFF)
private val onTertiaryContainerDarkHighContrast = Color(0xFF000000)
private val errorDarkHighContrast = Color(0xFFFFF9F9)
private val onErrorDarkHighContrast = Color(0xFF000000)
private val errorContainerDarkHighContrast = Color(0xFFFFBAB1)
private val onErrorContainerDarkHighContrast = Color(0xFF000000)
private val backgroundDarkHighContrast = Color(0xFF10150F)
private val onBackgroundDarkHighContrast = Color(0xFFE0E4DA)
private val surfaceDarkHighContrast = Color(0xFF10150F)
private val onSurfaceDarkHighContrast = Color(0xFFFFFFFF)
private val surfaceVariantDarkHighContrast = Color(0xFF40493E)
private val onSurfaceVariantDarkHighContrast = Color(0xFFF4FEED)
private val outlineDarkHighContrast = Color(0xFFC4CEBE)
private val outlineVariantDarkHighContrast = Color(0xFFC4CEBE)
private val scrimDarkHighContrast = Color(0xFF000000)
private val inverseSurfaceDarkHighContrast = Color(0xFFE0E4DA)
private val inverseOnSurfaceDarkHighContrast = Color(0xFF000000)
private val inversePrimaryDarkHighContrast = Color(0xFF003207)
private val surfaceDimDarkHighContrast = Color(0xFF10150F)
private val surfaceBrightDarkHighContrast = Color(0xFF363A34)
private val surfaceContainerLowestDarkHighContrast = Color(0xFF0B0F0A)
private val surfaceContainerLowDarkHighContrast = Color(0xFF181D17)
private val surfaceContainerDarkHighContrast = Color(0xFF1C211B)
private val surfaceContainerHighDarkHighContrast = Color(0xFF272B25)
private val surfaceContainerHighestDarkHighContrast = Color(0xFF313630)

val darkFernLightScheme = lightColorScheme(
    primary = primaryLight,
    onPrimary = onPrimaryLight,
    primaryContainer = primaryContainerLight,
    onPrimaryContainer = onPrimaryContainerLight,
    secondary = secondaryLight,
    onSecondary = onSecondaryLight,
    secondaryContainer = secondaryContainerLight,
    onSecondaryContainer = onSecondaryContainerLight,
    tertiary = tertiaryLight,
    onTertiary = onTertiaryLight,
    tertiaryContainer = tertiaryContainerLight,
    onTertiaryContainer = onTertiaryContainerLight,
    error = errorLight,
    onError = onErrorLight,
    errorContainer = errorContainerLight,
    onErrorContainer = onErrorContainerLight,
    background = backgroundLight,
    onBackground = onBackgroundLight,
    surface = surfaceLight,
    onSurface = onSurfaceLight,
    surfaceVariant = surfaceVariantLight,
    onSurfaceVariant = onSurfaceVariantLight,
    outline = outlineLight,
    outlineVariant = outlineVariantLight,
    scrim = scrimLight,
    inverseSurface = inverseSurfaceLight,
    inverseOnSurface = inverseOnSurfaceLight,
    inversePrimary = inversePrimaryLight,
    surfaceDim = surfaceDimLight,
    surfaceBright = surfaceBrightLight,
    surfaceContainerLowest = surfaceContainerLowestLight,
    surfaceContainerLow = surfaceContainerLowLight,
    surfaceContainer = surfaceContainerLight,
    surfaceContainerHigh = surfaceContainerHighLight,
    surfaceContainerHighest = surfaceContainerHighestLight,
)

val darkFernDarkScheme = darkColorScheme(
    primary = primaryDark,
    onPrimary = onPrimaryDark,
    primaryContainer = primaryContainerDark,
    onPrimaryContainer = onPrimaryContainerDark,
    secondary = secondaryDark,
    onSecondary = onSecondaryDark,
    secondaryContainer = secondaryContainerDark,
    onSecondaryContainer = onSecondaryContainerDark,
    tertiary = tertiaryDark,
    onTertiary = onTertiaryDark,
    tertiaryContainer = tertiaryContainerDark,
    onTertiaryContainer = onTertiaryContainerDark,
    error = errorDark,
    onError = onErrorDark,
    errorContainer = errorContainerDark,
    onErrorContainer = onErrorContainerDark,
    background = backgroundDark,
    onBackground = onBackgroundDark,
    surface = surfaceDark,
    onSurface = onSurfaceDark,
    surfaceVariant = surfaceVariantDark,
    onSurfaceVariant = onSurfaceVariantDark,
    outline = outlineDark,
    outlineVariant = outlineVariantDark,
    scrim = scrimDark,
    inverseSurface = inverseSurfaceDark,
    inverseOnSurface = inverseOnSurfaceDark,
    inversePrimary = inversePrimaryDark,
    surfaceDim = surfaceDimDark,
    surfaceBright = surfaceBrightDark,
    surfaceContainerLowest = surfaceContainerLowestDark,
    surfaceContainerLow = surfaceContainerLowDark,
    surfaceContainer = surfaceContainerDark,
    surfaceContainerHigh = surfaceContainerHighDark,
    surfaceContainerHighest = surfaceContainerHighestDark,
)
