<resources>

    <style name="AppTheme" parent="Theme.Material3.Light.NoActionBar">
        <item name="colorPrimary">@color/md_theme_primary</item>
        <item name="colorOnPrimary">@color/md_theme_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_onPrimaryContainer</item>
        <item name="colorSecondary">@color/md_theme_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_onSecondaryContainer</item>
        <item name="colorTertiary">@color/md_theme_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_onTertiaryContainer</item>
        <item name="colorError">@color/md_theme_error</item>
        <item name="colorOnError">@color/md_theme_onError</item>
        <item name="colorErrorContainer">@color/md_theme_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_onErrorContainer</item>
        <item name="android:colorBackground">@color/md_theme_background</item>
        <item name="colorOnBackground">@color/md_theme_onBackground</item>
        <item name="colorSurface">@color/md_theme_surface</item>
        <item name="colorOnSurface">@color/md_theme_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_outlineVariant</item>
        <item name="colorSurfaceInverse">@color/md_theme_inverseSurface</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_inverseOnSurface</item>
        <item name="colorPrimaryInverse">@color/md_theme_inversePrimary</item>
        <item name="colorPrimaryFixed">@color/md_theme_primaryFixed</item>
        <item name="colorOnPrimaryFixed">@color/md_theme_onPrimaryFixed</item>
        <item name="colorPrimaryFixedDim">@color/md_theme_primaryFixedDim</item>
        <item name="colorOnPrimaryFixedVariant">@color/md_theme_onPrimaryFixedVariant</item>
        <item name="colorSecondaryFixed">@color/md_theme_secondaryFixed</item>
        <item name="colorOnSecondaryFixed">@color/md_theme_onSecondaryFixed</item>
        <item name="colorSecondaryFixedDim">@color/md_theme_secondaryFixedDim</item>
        <item name="colorOnSecondaryFixedVariant">@color/md_theme_onSecondaryFixedVariant</item>
        <item name="colorTertiaryFixed">@color/md_theme_tertiaryFixed</item>
        <item name="colorOnTertiaryFixed">@color/md_theme_onTertiaryFixed</item>
        <item name="colorTertiaryFixedDim">@color/md_theme_tertiaryFixedDim</item>
        <item name="colorOnTertiaryFixedVariant">@color/md_theme_onTertiaryFixedVariant</item>
        <item name="colorSurfaceDim">@color/md_theme_surfaceDim</item>
        <item name="colorSurfaceBright">@color/md_theme_surfaceBright</item>
        <item name="colorSurfaceContainerLowest">@color/md_theme_surfaceContainerLowest</item>
        <item name="colorSurfaceContainerLow">@color/md_theme_surfaceContainerLow</item>
        <item name="colorSurfaceContainer">@color/md_theme_surfaceContainer</item>
        <item name="colorSurfaceContainerHigh">@color/md_theme_surfaceContainerHigh</item>
        <item name="colorSurfaceContainerHighest">@color/md_theme_surfaceContainerHighest</item>

        <item name="android:statusBarColor">?attr/colorSurface</item>
        <item name="android:navigationBarColor">?attr/colorSurface</item>

        <item name="navigationViewStyle">@style/NavigationView</item>
        <item name="bottomSheetDialogTheme">@style/ThemeOverlay.App.BottomSheetDialog</item>

        <item name="android:textColorLink">?attr/colorOnSurface</item>
        <item name="android:textColorHighlight">?attr/colorOnSurface</item>
        <item name="cursorColor">?attr/colorOnSurface</item>

        <item name="android:windowBackground">?attr/colorSurface</item>
    </style>

    <style name="NavigationView" parent="Widget.Material3.NavigationView">
        <item name="android:background">?attr/colorSurface</item>
    </style>

    <style name="FileChip" parent="Widget.Material3.Chip.Assist">
        <item name="android:stateListAnimator">@null</item>
        <item name="android:paddingBottom">16dp</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textAppearance">?attr/textAppearanceBodySmall</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
        <item name="chipIconSize">10pt</item>
        <item name="chipIconTint">?attr/colorOnSurface</item>
        <item name="chipStrokeColor">?attr/colorOnSurface</item>
        <item name="chipStartPadding">4dp</item>
        <item name="textStartPadding">2dp</item>
        <item name="android:clickable">false</item>
    </style>

    <style name="FileChip.ListItem">
        <item name="android:paddingBottom">4dp</item>
        <item name="android:drawablePadding">8dp</item>
    </style>

    <style name="TextView.Clickable" parent="Widget.AppCompat.TextView">
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
    </style>


    <style name="FilterChip" parent="Widget.Material3.Chip.Filter">
        <item name="checkedIcon">@null</item>
    </style>

    <style name="Preference" parent="TextView.Clickable">
        <item name="android:padding">16dp</item>
        <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
    </style>

    <style name="PreferenceHeader" parent="Widget.AppCompat.TextView">
        <item name="android:paddingEnd">16dp</item>
        <item name="android:paddingTop">16dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:textColor">?attr/colorPrimary</item>
        <item name="android:textAppearance">?attr/textAppearanceSubtitle2</item>
    </style>

    <style name="WidgetPreview.ListItem" parent="Widget.AppCompat.TextView">
        <item name="android:drawablePadding">8dp</item>
        <item name="android:drawableStart">@drawable/checkbox_outline_16</item>
        <item name="android:textAppearance">@style/TextAppearance.MaterialComponents.Body2</item>
    </style>

    <style name="BottomSheetText" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:padding">8dp</item>
        <item name="android:drawablePadding">16dp</item>
        <item name="android:clickable">true</item>
        <item name="android:layout_marginBottom">10dp</item>
    </style>

    <style name="ThemeOverlay.App.BottomSheetDialog" parent="ThemeOverlay.Material3.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/ModalBottomSheet</item>
        <item name="android:windowIsFloating">true</item>
    </style>

    <style name="ModalBottomSheet" parent="Widget.Material3.BottomSheet.Modal">
        <item name="shapeAppearance">@style/ShapeAppearance.Material3.Corner.None</item>
        <item name="behavior_draggable">false</item>
    </style>

    <style name="FloatingActionButtonPrimary" parent="Widget.Material3.FloatingActionButton.Primary">
        <item name="android:backgroundTint">?attr/colorPrimary</item>
        <item name="tint">?attr/colorOnPrimary</item>
    </style>

    <style name="RoundedTextInput">
        <item name="boxBackgroundMode">filled</item>
        <item name="boxBackgroundColor">?attr/colorSecondaryContainer</item>
        <item name="boxStrokeWidth">0dp</item>
        <item name="boxStrokeWidthFocused">0dp</item>
        <item name="hintEnabled">false</item>
        <item name="shapeAppearance">@style/RoundedCorners</item>
    </style>

    <style name="RoundedCorners">
        <item name="cornerSize">50%</item>
    </style>

</resources>
