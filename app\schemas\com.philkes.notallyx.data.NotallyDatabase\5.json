{"formatVersion": 1, "database": {"version": 5, "identityHash": "a0ebadcc625f8b49bf549975d7288f10", "entities": [{"tableName": "BaseNote", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `type` TEXT NOT NULL, `folder` TEXT NOT NULL, `color` TEXT NOT NULL, `title` TEXT NOT NULL, `pinned` INTEGER NOT NULL, `timestamp` INTEGER NOT NULL, `modifiedTimestamp` INTEGER NOT NULL, `labels` TEXT NOT NULL, `body` TEXT NOT NULL, `spans` TEXT NOT NULL, `items` TEXT NOT NULL, `images` TEXT NOT NULL, `files` TEXT NOT NULL, `audios` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "folder", "columnName": "folder", "affinity": "TEXT", "notNull": true}, {"fieldPath": "color", "columnName": "color", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "pinned", "columnName": "pinned", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "modifiedTimestamp", "columnName": "modifiedTimestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "labels", "columnName": "labels", "affinity": "TEXT", "notNull": true}, {"fieldPath": "body", "columnName": "body", "affinity": "TEXT", "notNull": true}, {"fieldPath": "spans", "columnName": "spans", "affinity": "TEXT", "notNull": true}, {"fieldPath": "items", "columnName": "items", "affinity": "TEXT", "notNull": true}, {"fieldPath": "images", "columnName": "images", "affinity": "TEXT", "notNull": true}, {"fieldPath": "files", "columnName": "files", "affinity": "TEXT", "notNull": true}, {"fieldPath": "audios", "columnName": "audios", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_BaseNote_id_folder_pinned_timestamp_labels", "unique": false, "columnNames": ["id", "folder", "pinned", "timestamp", "labels"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_BaseNote_id_folder_pinned_timestamp_labels` ON `${TABLE_NAME}` (`id`, `folder`, `pinned`, `timestamp`, `labels`)"}], "foreignKeys": []}, {"tableName": "Label", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`value` TEXT NOT NULL, PRIMARY KEY(`value`))", "fields": [{"fieldPath": "value", "columnName": "value", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["value"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'a0ebadcc625f8b49bf549975d7288f10')"]}}