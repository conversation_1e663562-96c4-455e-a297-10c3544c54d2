<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/Toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:navigationIcon="?attr/homeAsUpIndicator"
        app:title="@string/select_labels" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/MainListView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/Toolbar"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    <TextView
        android:id="@+id/EmptyState"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/Toolbar"
        android:gravity="center"
        android:text="@string/empty_labels"
        android:textAppearance="?attr/textAppearanceBodyMedium" />

</RelativeLayout>