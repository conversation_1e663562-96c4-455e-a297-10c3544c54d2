<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/Layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:colorBackground"
    android:theme="@style/AppTheme">

    <ListView
        android:id="@+id/ListView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:divider="@null" />

    <TextView
        android:id="@+id/Empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?android:selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:text="@string/select_note"
        android:textAppearance="?attr/textAppearanceSubtitle2"
        android:textColor="?android:textColorPrimary" />

</FrameLayout>