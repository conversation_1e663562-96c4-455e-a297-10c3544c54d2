pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
        mavenLocal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        mavenLocal()
    }
}
rootProject.name = "WeatherYou"
include ':app'
include ':data'
include ':domain'
include ':core'
include ':wearos'
include ':features:home'
include ':common:components'
include ':common:weathericons'
include ':features:addlocation'
include ':features:widgets'
include ':features:about'
include ':features:settings'
include ':features:locationdetails'
include ':tv'
include ':ads'
include ':billing'
