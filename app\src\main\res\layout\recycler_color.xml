<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="4dp">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/CardView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:cardCornerRadius="0dp"
        app:cardElevation="0dp"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintTop_toTopOf="parent"
        android:checkable="true"
        app:strokeWidth="1dp" >

        <ImageView
          android:id="@+id/CardIcon"
          android:layout_width="match_parent"
          android:layout_height="match_parent"
          android:contentDescription="CardIcon"
          android:padding="18dp"
          android:layout_gravity="center"
          android:visibility="gone"
          />
    </com.google.android.material.card.MaterialCardView>

</androidx.constraintlayout.widget.ConstraintLayout>