<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingStart="?attr/dialogPreferredPadding"
    android:paddingTop="8dp"
    android:paddingEnd="?attr/dialogPreferredPadding"
    android:paddingBottom="8dp">

    <TextView
        android:id="@+id/Name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textAppearance="?attr/textAppearanceBodyMedium" />

    <TextView
        android:id="@+id/Description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textAppearance="?attr/textAppearanceBodySmall" />

</LinearLayout>