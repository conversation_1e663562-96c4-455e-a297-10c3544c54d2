<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    android:layout_marginBottom="8dp"
    android:checkable="true"
    app:cardElevation="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="16dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/ImageLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/ImageView"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintDimensionRatio="4:3"
                app:layout_constraintTop_toTopOf="parent"
                />

            <com.google.android.material.chip.Chip
                android:id="@+id/ImageViewMore"
                style="@style/FileChip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false"
                android:stateListAnimator="@null"
                android:textAppearance="?attr/textAppearanceBodySmall"
                android:visibility="visible"
                app:chipIcon="@drawable/add_images"
                app:chipStartPadding="8dp"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                app:layout_constraintEnd_toEndOf="@id/ImageView"
                app:layout_constraintTop_toTopOf="@id/ImageView"
                app:textStartPadding="2dp"
                android:layout_marginEnd="8dp"
                android:layout_marginTop="8dp"
                />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <TextView
            android:id="@+id/Message"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:gravity="center"
            android:clickable="false"
            android:textIsSelectable="false"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:text="@string/cant_load_image"
            android:textAppearance="?attr/textAppearanceBodyMedium"
            app:layout_constraintDimensionRatio="4:3"
            app:layout_constraintTop_toBottomOf="@id/ImageLayout" />

        <Space
            android:id="@+id/Space"
            android:layout_width="match_parent"
            android:layout_height="14dp"
            android:clickable="false"
            app:layout_constraintTop_toBottomOf="@id/Message" />

        <LinearLayout
          android:id="@+id/TitleLayout"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:orientation="horizontal"
          android:paddingStart="16dp"
          android:paddingEnd="16dp"
          app:layout_constraintTop_toBottomOf="@id/Space">

            <TextView
              android:id="@+id/Title"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:fontFamily="sans-serif-medium"
              android:textAppearance="?attr/textAppearanceBodyLarge"
              android:textColor="?attr/colorOnSurface"
              android:ellipsize="end"
              android:textIsSelectable="false"
              android:layout_weight="9"/>

            <ImageView
              android:id="@+id/RemindersView"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:clickable="false"
              android:contentDescription="@string/reminders"
              android:stateListAnimator="@null"
              android:textAppearance="?attr/textAppearanceBodySmall"
              android:visibility="visible"
              android:src="@drawable/notifications"
              android:layout_weight="1"
              />
        </LinearLayout>

        <TextView
            android:id="@+id/Date"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="6dp"
            android:textIsSelectable="false"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            app:layout_constraintTop_toBottomOf="@id/TitleLayout" />

        <LinearLayout
            android:id="@+id/FileViewLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="0dp"
            android:paddingBottom="4dp"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            app:layout_constraintTop_toBottomOf="@id/Date"
            >
            <com.google.android.material.chip.Chip
                android:id="@+id/FileView"
                style="@style/FileChip"
                android:visibility="visible"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:chipIcon="@drawable/text_file"
                android:text="@string/cant_load_file"
                app:layout_constraintStart_toStartOf="parent"
                />

            <com.google.android.material.chip.Chip
                android:id="@+id/FileViewMore"
                style="@style/FileChip"
                android:visibility="visible"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:layout_marginEnd="6dp"
                android:textAppearance="?attr/textAppearanceBodySmall"
                app:chipStartPadding="8dp"
                app:textStartPadding="2dp"
                />
        </LinearLayout>

        <TextView
            android:id="@+id/Note"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:lineSpacingMultiplier="1.1"
            android:ellipsize="end"
            android:textIsSelectable="false"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            app:layout_constraintTop_toBottomOf="@id/FileViewLayout" />

        <LinearLayout
            android:id="@+id/LinearLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            app:layout_constraintTop_toBottomOf="@id/Note">

            <TextView
                style="@style/FileChip.ListItem"
                android:textIsSelectable="false"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <TextView
                style="@style/FileChip.ListItem"
                android:textIsSelectable="false"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <TextView
                style="@style/FileChip.ListItem"
                android:textIsSelectable="false"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <TextView
                style="@style/FileChip.ListItem"
                android:textIsSelectable="false"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <TextView
                style="@style/FileChip.ListItem"
                android:textIsSelectable="false"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <TextView
                style="@style/FileChip.ListItem"
                android:textIsSelectable="false"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <TextView
                style="@style/FileChip.ListItem"
                android:textIsSelectable="false"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <TextView
                style="@style/FileChip.ListItem"
                android:textIsSelectable="false"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <TextView
                style="@style/FileChip.ListItem"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <TextView
                style="@style/FileChip.ListItem"
                android:textIsSelectable="false"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/ItemsRemaining"
                style="@style/FileChip.ListItem"
                android:textIsSelectable="false"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:drawableStartCompat="@drawable/add_16" />

        </LinearLayout>

        <com.google.android.material.chip.ChipGroup
            android:id="@+id/LabelGroup"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:stateListAnimator="@null"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            app:layout_constraintTop_toBottomOf="@id/LinearLayout" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>