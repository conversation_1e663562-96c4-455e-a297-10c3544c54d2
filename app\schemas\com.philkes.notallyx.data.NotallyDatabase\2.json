{"formatVersion": 1, "database": {"version": 2, "identityHash": "4b4b549bcedcae938a5da7fd5433af4f", "entities": [{"tableName": "BaseNote", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `type` TEXT NOT NULL, `folder` TEXT NOT NULL, `color` TEXT NOT NULL, `title` TEXT NOT NULL, `pinned` INTEGER NOT NULL, `timestamp` INTEGER NOT NULL, `labels` TEXT NOT NULL, `body` TEXT NOT NULL, `spans` TEXT NOT NULL, `items` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "folder", "columnName": "folder", "affinity": "TEXT", "notNull": true}, {"fieldPath": "color", "columnName": "color", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "pinned", "columnName": "pinned", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "labels", "columnName": "labels", "affinity": "TEXT", "notNull": true}, {"fieldPath": "body", "columnName": "body", "affinity": "TEXT", "notNull": true}, {"fieldPath": "spans", "columnName": "spans", "affinity": "TEXT", "notNull": true}, {"fieldPath": "items", "columnName": "items", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [{"name": "index_BaseNote_id_folder_pinned_timestamp_labels", "unique": false, "columnNames": ["id", "folder", "pinned", "timestamp", "labels"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_BaseNote_id_folder_pinned_timestamp_labels` ON `${TABLE_NAME}` (`id`, `folder`, `pinned`, `timestamp`, `labels`)"}], "foreignKeys": []}, {"tableName": "Label", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`value` TEXT NOT NULL, PRIMARY KEY(`value`))", "fields": [{"fieldPath": "value", "columnName": "value", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["value"], "autoGenerate": false}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '4b4b549bcedcae938a5da7fd5433af4f')"]}}