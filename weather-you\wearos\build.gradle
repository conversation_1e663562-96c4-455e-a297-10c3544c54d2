plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    alias(libs.plugins.compose.compiler)
}

def keyStorePropertiesFile = rootProject.file("keystore.properties")
//def keyStoreProperties = new Properties()
//keyStoreProperties.load(new FileInputStream(keyStorePropertiesFile))

android {
    namespace 'com.rodrigmatrix.weatheryou.wearos'
    compileSdk Sdk.compileSdk

    defaultConfig {
        applicationId Sdk.applicationId
        minSdk Sdk.wearMinSdk
        targetSdk Sdk.targetSdk
        versionCode 1
        versionName "1.0"
        vectorDrawables {
            useSupportLibrary true
        }
    }
    signingConfigs {
//        release {
//            keyAlias keyStoreProperties['releaseKeyAlias']
//            keyPassword keyStoreProperties['releaseKeyPassword']
//            storeFile file(keyStoreProperties['releaseKeyStore'])
//            storePassword keyStoreProperties['releaseStorePassword']
//        }
    }
    buildTypes {
        release {
//            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            applicationIdSuffix ".debug"
            debuggable true
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion '1.1.0-rc02'
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
}

dependencies {
    implementation project(LocalModules.domain)
    implementation project(LocalModules.data)
    implementation project(LocalModules.core)
    implementation project(LocalModules.components)
    implementation project(LocalModules.weatherIcons)

    implementation(libs.androidx.ktx)
    implementation(libs.androidx.lifecycle)
    implementation(libs.androidx.splash.screen)

     implementation platform(libs.compose.bom)
    implementation(libs.compose.ui)
    implementation(libs.compose.wearable.material)
    implementation(libs.compose.wearable.foundation)
    implementation(libs.android.material)
    implementation(libs.compose.preview)
    implementation(libs.compose.activity)
    implementation(libs.accompanist.permissions)
    implementation(libs.compose.constraint.layout)

    implementation(libs.androidx.watchface)
    implementation(libs.androidx.watchface.complications.data.source)
    implementation(libs.androidx.watchface.complications.data.source.ktx)
    implementation(libs.androidx.watchface.complications.rendering)
    implementation(libs.androidx.watchface.editor)

    implementation platform(libs.firebase.bom)
    implementation(libs.firebase.remote.config)
    implementation(libs.firebase.crashlytics)
    implementation(libs.firebase.analytics)

    implementation(libs.jodaTime)

    implementation(libs.lottie.compose)

    implementation(libs.koin.android)
    implementation(libs.koin.compose)

    implementation(libs.playServices.wearable)

    debugImplementation(libs.composeUiTooling)
    debugImplementation(libs.composeTestManifest)

    androidTestImplementation(libs.composeUiTestJunit)
}