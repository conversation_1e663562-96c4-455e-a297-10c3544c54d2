package com.rodrigmatrix.weatheryou.tv.presentation.theme

import androidx.compose.ui.graphics.Color
import androidx.tv.material3.darkColorScheme
import androidx.tv.material3.lightColorScheme

private val primaryLight = Color(0xFF006875)
private val onPrimaryLight = Color(0xFFFFFFFF)
private val primaryContainerLight = Color(0xFF78EBFF)
private val onPrimaryContainerLight = Color(0xFF004B54)
private val secondaryLight = Color(0xFF38656D)
private val onSecondaryLight = Color(0xFFFFFFFF)
private val secondaryContainerLight = Color(0xFFBDECF5)
private val onSecondaryContainerLight = Color(0xFF204F57)
private val tertiaryLight = Color(0xFF695682)
private val onTertiaryLight = Color(0xFFFFFFFF)
private val tertiaryContainerLight = Color(0xFFE7D3FF)
private val onTertiaryContainerLight = Color(0xFF4D3B65)
private val errorLight = Color(0xFFBA1A1A)
private val onErrorLight = Color(0xFFFFFFFF)
private val errorContainerLight = Color(0xFFFFDAD6)
private val onErrorContainerLight = Color(0xFF410002)
private val backgroundLight = Color(0xFFF5FAFC)
private val onBackgroundLight = Color(0xFF161D1E)
private val surfaceLight = Color(0xFFF5FAFC)
private val onSurfaceLight = Color(0xFF161D1E)
private val surfaceVariantLight = Color(0xFFD7E5E8)
private val onSurfaceVariantLight = Color(0xFF3C494B)
private val scrimLight = Color(0xFF000000)
private val inverseSurfaceLight = Color(0xFF2B3133)
private val inverseOnSurfaceLight = Color(0xFFECF2F3)
private val inversePrimaryLight = Color(0xFF4BD8EE)
private val primaryDark = Color(0xFFF4FDFF)
private val onPrimaryDark = Color(0xFF00363D)
private val primaryContainerDark = Color(0xFF58E3F8)
private val onPrimaryContainerDark = Color(0xFF00444D)
private val secondaryDark = Color(0xFFA0CED7)
private val onSecondaryDark = Color(0xFF00363D)
private val secondaryContainerDark = Color(0xFF11434B)
private val onSecondaryContainerDark = Color(0xFFAAD8E2)
private val tertiaryDark = Color(0xFFFFFBFF)
private val onTertiaryDark = Color(0xFF392851)
private val tertiaryContainerDark = Color(0xFFDFC8FB)
private val onTertiaryContainerDark = Color(0xFF47355F)
private val errorDark = Color(0xFFFFB4AB)
private val onErrorDark = Color(0xFF690005)
private val errorContainerDark = Color(0xFF93000A)
private val onErrorContainerDark = Color(0xFFFFDAD6)
private val backgroundDark = Color(0xFF0E1416)
private val onBackgroundDark = Color(0xFFDEE3E5)
private val surfaceDark = Color(0xFF0E1416)
private val onSurfaceDark = Color(0xFFDEE3E5)
private val surfaceVariantDark = Color(0xFF3C494B)
private val onSurfaceVariantDark = Color(0xFFBBC9CC)
private val scrimDark = Color(0xFF000000)
private val inverseSurfaceDark = Color(0xFFDEE3E5)
private val inverseOnSurfaceDark = Color(0xFF2B3133)
private val inversePrimaryDark = Color(0xFF006875)

internal val mosqueLightScheme = lightColorScheme(
    primary = primaryLight,
    onPrimary = onPrimaryLight,
    primaryContainer = primaryContainerLight,
    onPrimaryContainer = onPrimaryContainerLight,
    secondary = secondaryLight,
    onSecondary = onSecondaryLight,
    secondaryContainer = secondaryContainerLight,
    onSecondaryContainer = onSecondaryContainerLight,
    tertiary = tertiaryLight,
    onTertiary = onTertiaryLight,
    tertiaryContainer = tertiaryContainerLight,
    onTertiaryContainer = onTertiaryContainerLight,
    error = errorLight,
    onError = onErrorLight,
    errorContainer = errorContainerLight,
    onErrorContainer = onErrorContainerLight,
    background = backgroundLight,
    onBackground = onBackgroundLight,
    surface = surfaceLight,
    onSurface = onSurfaceLight,
    surfaceVariant = surfaceVariantLight,
    onSurfaceVariant = onSurfaceVariantLight,
    scrim = scrimLight,
    inverseSurface = inverseSurfaceLight,
    inverseOnSurface = inverseOnSurfaceLight,
    inversePrimary = inversePrimaryLight,
)

internal val mosqueDarkScheme = darkColorScheme(
    primary = primaryDark,
    onPrimary = onPrimaryDark,
    primaryContainer = primaryContainerDark,
    onPrimaryContainer = onPrimaryContainerDark,
    secondary = secondaryDark,
    onSecondary = onSecondaryDark,
    secondaryContainer = secondaryContainerDark,
    onSecondaryContainer = onSecondaryContainerDark,
    tertiary = tertiaryDark,
    onTertiary = onTertiaryDark,
    tertiaryContainer = tertiaryContainerDark,
    onTertiaryContainer = onTertiaryContainerDark,
    error = errorDark,
    onError = onErrorDark,
    errorContainer = errorContainerDark,
    onErrorContainer = onErrorContainerDark,
    background = backgroundDark,
    onBackground = onBackgroundDark,
    surface = surfaceDark,
    onSurface = onSurfaceDark,
    surfaceVariant = surfaceVariantDark,
    onSurfaceVariant = onSurfaceVariantDark,
    scrim = scrimDark,
    inverseSurface = inverseSurfaceDark,
    inverseOnSurface = inverseOnSurfaceDark,
    inversePrimary = inversePrimaryDark,
)