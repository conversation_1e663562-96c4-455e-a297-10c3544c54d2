<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            style="@style/PreferenceHeader"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/appearance" />

        <include
            android:id="@+id/View"
            layout="@layout/preference" />

        <include
            android:id="@+id/Theme"
            layout="@layout/preference" />

        <include
            android:id="@+id/DateFormat"
            layout="@layout/preference" />

        <include
            android:id="@+id/TextSize"
            layout="@layout/preference" />

        <View
          android:layout_width="match_parent"
          android:layout_height="1dp"
          android:background="?android:attr/listDivider" />

        <TextView
          style="@style/PreferenceHeader"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:text="@string/behaviour" />

        <include
            android:id="@+id/NotesSortOrder"
            layout="@layout/preference" />

        <include
            android:id="@+id/CheckedListItemSorting"
            layout="@layout/preference" />

        <include
          android:id="@+id/StartView"
          layout="@layout/preference" />

        <include
          android:id="@+id/AutoSaveAfterIdle"
          layout="@layout/preference_seekbar" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="?android:attr/listDivider" />

        <TextView
            style="@style/PreferenceHeader"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/content_density" />

        <include
            android:id="@+id/MaxTitle"
            layout="@layout/preference_seekbar" />

        <include
            android:id="@+id/MaxItems"
            layout="@layout/preference_seekbar" />

        <include
            android:id="@+id/MaxLines"
            layout="@layout/preference_seekbar" />

        <include
          android:id="@+id/MaxLabels"
          layout="@layout/preference_seekbar" />

        <include
          android:id="@+id/LabelsHiddenInOverview"
          layout="@layout/preference" />

        <include
          android:id="@+id/ImagesHiddenInOverview"
          layout="@layout/preference" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="?android:attr/listDivider" />

        <TextView
            style="@style/PreferenceHeader"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/backup" />

        <TextView
            android:id="@+id/ImportBackup"
            style="@style/Preference"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/import_backup" />

        <TextView
          android:id="@+id/ImportOther"
          style="@style/Preference"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:text="@string/import_other" />

        <TextView
            android:id="@+id/ExportBackup"
            style="@style/Preference"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/export_backup" />

        <TextView
          style="@style/PreferenceHeader"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:text="@string/auto_backup" />

        <include
          android:id="@+id/BackupsFolder"
          layout="@layout/preference" />

        <include
          android:id="@+id/BackupOnSave"
          layout="@layout/preference" />

        <include
          android:id="@+id/PeriodicBackups"
          layout="@layout/preference" />

        <TextView
          android:id="@+id/PeriodicBackupLastExecution"
          android:textAppearance="?attr/textAppearanceBodySmall"
          android:paddingStart="16dp"
          android:paddingEnd="16dp"
          android:layout_width="match_parent"
          android:layout_height="wrap_content" />

        <include
          android:id="@+id/PeriodicBackupsPeriodInDays"
          layout="@layout/preference_seekbar" />

        <include
          android:id="@+id/PeriodicBackupsMax"
          layout="@layout/preference_seekbar" />

        <View
          android:layout_width="match_parent"
          android:layout_height="1dp"
          android:background="?android:attr/listDivider" />

        <TextView
            style="@style/PreferenceHeader"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/security" />

        <include
            android:id="@+id/BiometricLock"
            layout="@layout/preference" />

        <include
          android:id="@+id/BackupPassword"
          layout="@layout/preference" />

        <include
          android:id="@+id/SecureFlag"
          layout="@layout/preference" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="?android:attr/listDivider" />

        <TextView
          style="@style/PreferenceHeader"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:text="@string/settings" />

        <TextView
          android:id="@+id/ImportSettings"
          style="@style/Preference"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:text="@string/import_settings" />

        <TextView
          android:id="@+id/ExportSettings"
          style="@style/Preference"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:text="@string/export_settings" />

        <TextView
          android:id="@+id/ResetSettings"
          style="@style/Preference"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:text="@string/reset_settings" />

        <include
          android:id="@+id/DataInPublicFolder"
          layout="@layout/preference" />

        <TextView
          android:id="@+id/ClearData"
          style="@style/Preference"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:text="@string/clear_data" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="?android:attr/listDivider" />

        <TextView
            style="@style/PreferenceHeader"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/about" />

        <TextView
          android:id="@+id/SendFeedback"
          style="@style/Preference"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:text="@string/send_feedback" />
        
        <TextView
          android:id="@+id/Rate"
          style="@style/Preference"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:text="@string/rate" />

        <TextView
          android:id="@+id/Donate"
          style="@style/Preference"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:text="@string/donate" />

        <TextView
            android:id="@+id/SourceCode"
            style="@style/Preference"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/source_code" />

        <TextView
          android:id="@+id/Libraries"
          style="@style/Preference"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:text="@string/libraries" />

        <TextView
          android:id="@+id/VersionText"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:textAppearance="?attr/textAppearanceBodySmall"
          android:gravity="start"
          android:padding="16dp" />

    </LinearLayout>

</androidx.core.widget.NestedScrollView>