<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/DrawerLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <RelativeLayout
        android:id="@+id/RelativeLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/Toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="Toolbar"
            app:collapseContentDescription="ToolbarCollapse"
            app:navigationContentDescription="ToolbarNavigation"
            app:contentInsetEnd="16dp">

        </com.google.android.material.appbar.MaterialToolbar>

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/ActionMode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/Toolbar"
            app:navigationIcon="@drawable/close" />

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/NavHostFragment"
            android:name="androidx.navigation.fragment.NavHostFragment"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/ActionMode"
            app:defaultNavHost="true"
            app:navGraph="@navigation/navigation" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/MakeList"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@id/TakeNote"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"
            android:contentDescription="@string/make_list"
            android:tooltipText="@string/make_list"
            style="@style/FloatingActionButtonPrimary"
            app:srcCompat="@drawable/checkbox" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/TakeNote"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"
            android:contentDescription="@string/take_note"
            android:tooltipText="@string/take_note"
            style="@style/FloatingActionButtonPrimary"
            app:srcCompat="@drawable/edit" />

    </RelativeLayout>

    <com.google.android.material.navigation.NavigationView
        android:id="@+id/NavigationView"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        app:headerLayout="@layout/drawer_header"
        app:itemIconTint="@color/navigation_view_item"
        app:itemTextColor="@color/navigation_view_item"
        app:drawerLayoutCornerSize="0dp"/>

</androidx.drawerlayout.widget.DrawerLayout>