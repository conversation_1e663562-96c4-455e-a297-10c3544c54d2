plugins {
    id("com.android.library")
    id("org.jetbrains.kotlin.android")
    alias(libs.plugins.compose.compiler)
}

android {
    namespace = "com.rodrigmatrix.weatheryou.about"
    compileSdk Sdk.compileSdk

    defaultConfig {
        minSdk Sdk.phoneMinSdk
        targetSdk Sdk.targetSdk

        def localProperties = new Properties()
        if (rootProject.file("local.properties").exists()) {
            localProperties.load(new FileInputStream(rootProject.file("local.properties")))
        }
        def password = System.getenv("WEATHERYOU_PREMIUM_PASSWORD") ?: localProperties['weatheryou.premiumpassword']
        buildConfigField "String", "PREMIUM_PASSWORD", "\"" + password + "\""

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    kotlin.sourceSets.configureEach {
        languageSettings.optIn("kotlin.RequiresOptIn")
    }
    buildFeatures {
        compose true
        buildConfig true
    }
    composeOptions {
        kotlinCompilerExtensionVersion libs.versions.composeCompilerVersion.get()
    }
}

dependencies {
    implementation project(LocalModules.core)
    implementation project(LocalModules.components)
    implementation project(LocalModules.weatherIcons)
    implementation project(LocalModules.domain)

    implementation(libs.androidx.ktx)
    implementation(libs.androidx.lifecycle)
    implementation(libs.androidx.window)

    implementation(libs.kotlin.stdlib)
    implementation(libs.kotlin.coroutines.android)
    implementation(libs.kotlin.serialization)

    implementation platform(libs.compose.bom)
    implementation(libs.compose.ui)
    implementation(libs.compose.runtime)
    implementation(libs.compose.material3)
    implementation(libs.compose.material)
    implementation(libs.compose.preview)
    implementation(libs.compose.activity)
    implementation(libs.compose.navigation)
    implementation(libs.compose.constraint.layout)
    implementation(libs.compose.navigation)
    implementation(libs.compose.window.size)
    implementation(libs.accompanist.permissions)
    implementation(libs.accompanist.navigation)
    implementation(libs.accompanist.adaptive)

    implementation(libs.koin.android)
    implementation(libs.koin.compose)

    implementation(libs.jodaTime)

    implementation(libs.coil)

    implementation(libs.lottie.compose)

    debugImplementation(libs.composeUiTooling)
    debugImplementation(libs.composeTestManifest)

    testImplementation(libs.junit)
    androidTestImplementation(libs.testExtJunit)
    androidTestImplementation(libs.espresso.core)
    androidTestImplementation(libs.composeUiTestJunit)
}