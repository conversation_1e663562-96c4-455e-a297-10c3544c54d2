package io.despicable.chromeos.presentation.ui.widget

import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.glance.GlanceId
import androidx.glance.GlanceModifier
import androidx.glance.GlanceTheme
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.GlanceAppWidgetManager
import androidx.glance.appwidget.provideContent
import androidx.glance.appwidget.state.updateAppWidgetState
import androidx.glance.background
import androidx.glance.currentState
import androidx.glance.layout.Alignment
import androidx.glance.layout.Box
import androidx.glance.layout.Column
import androidx.glance.layout.Row
import androidx.glance.layout.Spacer
import androidx.glance.layout.fillMaxSize
import androidx.glance.layout.fillMaxWidth
import androidx.glance.layout.height
import androidx.glance.layout.padding
import androidx.glance.layout.width
import androidx.glance.state.GlanceStateDefinition
import androidx.glance.state.PreferencesGlanceStateDefinition
import androidx.glance.text.FontWeight
import androidx.glance.text.Text
import androidx.glance.text.TextStyle
import androidx.glance.unit.ColorProvider
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.stringPreferencesKey
import io.despicable.chromeos.data.database.WeatherDatabase
import io.despicable.chromeos.data.database.entity.toDomainModel
import io.despicable.chromeos.data.datastore.WidgetPreferences
import io.despicable.chromeos.domain.model.WeatherCondition
import io.despicable.chromeos.domain.model.WeatherData
import io.despicable.chromeos.domain.model.WidgetConfiguration
import io.despicable.chromeos.util.SampleWeatherData
import kotlinx.coroutines.flow.first
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

/**
 * Jetpack Glance weather widget
 */
@SuppressLint("RestrictedApi")
class WeatherWidget : GlanceAppWidget() {

    companion object {
        private const val TAG = "WeatherWidget"

        // State keys for Glance preferences
        private val WEATHER_DATA_KEY = stringPreferencesKey("weather_data")
        private val CONFIG_DATA_KEY = stringPreferencesKey("config_data")
        private val ERROR_MESSAGE_KEY = stringPreferencesKey("error_message")
    }

    override val stateDefinition: GlanceStateDefinition<*> = PreferencesGlanceStateDefinition

    override suspend fun provideGlance(context: Context, id: GlanceId) {
        Log.d(TAG, "🚀 WIDGET PROVIDEGLANCE CALLED! GlanceId: $id")

        provideContent {
            Log.d(TAG, "✅ Providing content to widget")
            val prefs = currentState<Preferences>()

            val weatherDataJson = prefs[WEATHER_DATA_KEY]
            val configDataJson = prefs[CONFIG_DATA_KEY]
            val errorMessage = prefs[ERROR_MESSAGE_KEY]

            GlanceTheme {
                when {
                    errorMessage != null -> {
                        Log.d(TAG, "Displaying error: $errorMessage")
                        ErrorContent(errorMessage)
                    }
                    weatherDataJson != null && configDataJson != null -> {
                        Log.d(TAG, "Displaying weather data from state")
                        // Parse data outside of composable
                        val parsedData = try {
                            val weatherData = Json.decodeFromString<WeatherData>(weatherDataJson)
                            val config = Json.decodeFromString<WidgetConfiguration>(configDataJson)
                            Pair(weatherData, config)
                        } catch (e: Exception) {
                            Log.e(TAG, "Error parsing widget state", e)
                            null
                        }

                        if (parsedData != null) {
                            WeatherWidgetContent(weatherData = parsedData.first, config = parsedData.second)
                        } else {
                            ErrorContent("Failed to parse widget data")
                        }
                    }
                    else -> {
                        Log.d(TAG, "No data available, showing loading state")
                        LoadingContent()
                    }
                }
            }
        }
        Log.d(TAG, "✅ Widget content provided successfully")
    }

    @Composable
    private fun WeatherWidgetContent(
        weatherData: WeatherData?,
        config: WidgetConfiguration?
    ) {
        // Log what we're rendering
        Log.d(TAG, "WeatherWidgetContent - weatherData: $weatherData")
        Log.d(TAG, "WeatherWidgetContent - config: $config")

        Box(
            modifier = GlanceModifier
                .fillMaxSize()
                .background(ColorProvider(Color.White))
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            if (weatherData != null && config != null) {
                Log.d(TAG, "Rendering widget with data and config")
                Column(
                    modifier = GlanceModifier.fillMaxSize(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // Weather icon (using emoji for now)
                    Text(
                        text = getWeatherEmoji(weatherData.weatherCondition),
                        style = TextStyle(
                            fontSize = config.iconSize.sizeDp.sp,
                            fontWeight = FontWeight.Bold
                        )
                    )

                    Spacer(modifier = GlanceModifier.height(8.dp))

                    // Temperature
                    Text(
                        text = "${weatherData.temperature}°C",
                        style = TextStyle(
                            fontSize = (config.fontSize.sizeSp + 4).sp,
                            fontWeight = FontWeight.Bold,
                            color = ColorProvider(Color.Black)
                        )
                    )

                    Spacer(modifier = GlanceModifier.height(4.dp))

                    // City name
                    Text(
                        text = weatherData.cityName,
                        style = TextStyle(
                            fontSize = config.fontSize.sizeSp.sp,
                            color = ColorProvider(Color.DarkGray)
                        )
                    )

                    if (config.showHumidity || config.showWindSpeed) {
                        Spacer(modifier = GlanceModifier.height(8.dp))

                        Row(
                            modifier = GlanceModifier.fillMaxWidth(),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            if (config.showHumidity) {
                                Text(
                                    text = "💧${weatherData.humidity}%",
                                    style = TextStyle(
                                        fontSize = (config.fontSize.sizeSp - 2).sp,
                                        color = ColorProvider(Color.Gray)
                                    )
                                )

                                if (config.showWindSpeed) {
                                    Spacer(modifier = GlanceModifier.width(12.dp))
                                }
                            }

                            if (config.showWindSpeed) {
                                Text(
                                    text = "💨${String.format("%.1f", weatherData.windSpeed)}km/h",
                                    style = TextStyle(
                                        fontSize = (config.fontSize.sizeSp - 2).sp,
                                        color = ColorProvider(Color.Gray)
                                    )
                                )
                            }
                        }
                    }
                }
            } else {
                // Loading or error state
                Log.d(
                    TAG,
                    "Rendering loading state - weatherData is null: ${weatherData == null}, config is null: ${config == null}"
                )
                Text(
                    text = "Loading...",
                    style = TextStyle(
                        fontSize = 16.sp,
                        color = ColorProvider(Color.Gray)
                    )
                )
            }
        }
    }

    @Composable
    private fun ErrorContent(message: String) {
        Box(
            modifier = GlanceModifier
                .fillMaxSize()
                .background(ColorProvider(Color.White))
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "Error: $message",
                style = TextStyle(
                    fontSize = 14.sp,
                    color = ColorProvider(Color.Red)
                )
            )
        }
    }

    @Composable
    private fun LoadingContent() {
        Box(
            modifier = GlanceModifier
                .fillMaxSize()
                .background(ColorProvider(Color.White))
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "Loading...",
                style = TextStyle(
                    fontSize = 16.sp,
                    color = ColorProvider(Color.Gray)
                )
            )
        }
    }

    private fun getWeatherEmoji(condition: WeatherCondition): String {
        return when (condition) {
            WeatherCondition.SUNNY -> "☀️"
            WeatherCondition.CLOUDY -> "☁️"
            WeatherCondition.RAINY -> "🌧️"
            WeatherCondition.SNOWY -> "❄️"
            WeatherCondition.STORMY -> "⛈️"
            WeatherCondition.FOGGY -> "🌫️"
            WeatherCondition.PARTLY_CLOUDY -> "⛅"
        }
    }

    /**
     * Update widget state with weather data and configuration
     */
    suspend fun updateWidgetState(context: Context, id: GlanceId) {
        try {
            Log.d(TAG, "🔄 Updating widget state for GlanceId: $id")

            val widgetId = getWidgetId(context, id)
            Log.d(TAG, "✅ Resolved widget ID: $widgetId")

            val config = getWidgetConfiguration(context, widgetId)
            Log.d(TAG, "✅ Widget configuration: $config")

            val cityName = config?.cityName ?: "New York"
            Log.d(TAG, "✅ Loading weather data for city: $cityName")

            val weatherData = getWeatherData(context, cityName)
            Log.d(TAG, "✅ Weather data loaded: $weatherData")

            // Update the widget state
            updateAppWidgetState(context, PreferencesGlanceStateDefinition, id) { prefs ->
                prefs.toMutablePreferences().apply {
                    if (weatherData != null && config != null) {
                        this[WEATHER_DATA_KEY] = Json.encodeToString(weatherData)
                        this[CONFIG_DATA_KEY] = Json.encodeToString(config)
                        remove(ERROR_MESSAGE_KEY) // Clear any previous error
                        Log.d(TAG, "✅ Widget state updated with weather data")
                    } else {
                        this[ERROR_MESSAGE_KEY] = "No data available"
                        remove(WEATHER_DATA_KEY)
                        remove(CONFIG_DATA_KEY)
                        Log.d(TAG, "⚠️ Widget state updated with error message")
                    }
                }
            }

            // Update the widget UI
            update(context, id)
            Log.d(TAG, "✅ Widget state update completed")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error updating widget state", e)
            // Update state with error message
            updateAppWidgetState(context, PreferencesGlanceStateDefinition, id) { prefs ->
                prefs.toMutablePreferences().apply {
                    this[ERROR_MESSAGE_KEY] = e.message ?: "Unknown error"
                    remove(WEATHER_DATA_KEY)
                    remove(CONFIG_DATA_KEY)
                }
            }
            update(context, id)
        }
    }

    // Helper functions
    private suspend fun getWidgetId(context: Context, glanceId: GlanceId): Int {
        return try {
            // Get the actual AppWidget ID from GlanceAppWidgetManager
            val glanceAppWidgetManager = GlanceAppWidgetManager(context)
            val appWidgetId = glanceAppWidgetManager.getAppWidgetId(glanceId)
            Log.d(TAG, "✅ GlanceId $glanceId mapped to AppWidget ID: $appWidgetId")
            appWidgetId
        } catch (e: Exception) {
            Log.e(TAG, "⚠️ Error getting widget ID from GlanceId: $glanceId", e)
            // Fallback: use a consistent hash of the GlanceId string
            val fallbackId = glanceId.toString().hashCode().let { if (it < 0) -it else it }
            Log.d(TAG, "⚠️ Using fallback widget ID: $fallbackId")
            fallbackId
        }
    }

    private suspend fun getWidgetConfiguration(
        context: Context,
        widgetId: Int
    ): WidgetConfiguration? {
        return try {
            Log.d(TAG, "Loading widget configuration for ID: $widgetId")
            val widgetPreferences = WidgetPreferences(context)
            val config = widgetPreferences.getWidgetConfig(widgetId).first()
            if (config != null) {
                Log.d(TAG, "Found widget configuration: $config")
            } else {
                Log.d(TAG, "No widget configuration found for ID: $widgetId")
            }
            config
        } catch (e: Exception) {
            Log.e(TAG, "Error loading widget configuration for ID: $widgetId", e)
            null
        }
    }

    private suspend fun getWeatherData(context: Context, cityName: String): WeatherData? {
        return try {
            Log.d(TAG, "Loading weather data for city: $cityName")
            val weatherDao = WeatherDatabase.getDatabase(context).weatherDao()
            val weatherEntity = weatherDao.getWeatherByCity(cityName)

            val weatherData = if (weatherEntity != null) {
                Log.d(TAG, "Found weather data in database for: $cityName")
                weatherEntity.toDomainModel()
            } else {
                Log.d(TAG, "No weather data in database, generating sample data for: $cityName")
                SampleWeatherData.generateRandomWeatherData(cityName)
            }

            Log.d(TAG, "Returning weather data: $weatherData")
            weatherData
        } catch (e: Exception) {
            Log.e(TAG, "Error loading weather data for city: $cityName", e)
            val fallbackData = SampleWeatherData.generateRandomWeatherData(cityName)
            Log.d(TAG, "Using fallback weather data: $fallbackData")
            fallbackData
        }
    }
}
