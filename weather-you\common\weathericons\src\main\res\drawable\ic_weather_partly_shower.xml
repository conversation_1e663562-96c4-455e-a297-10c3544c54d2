<vector android:height="24dp" android:viewportHeight="256"
    android:viewportWidth="256" android:width="24dp" xmlns:android="http://schemas.android.com/apk/res/android">
    <group>
        <clip-path android:pathData="M0,0h256v256h-256z"/>
        <path android:fillAlpha="0" android:fillColor="#FF000000"
            android:pathData="M83.89,12C114.16,12 138.73,36.23 138.73,66.07C138.73,95.9 114.16,120.13 83.89,120.13C53.63,120.13 29.06,95.9 29.06,66.07C29.06,36.23 53.63,12 83.89,12z"
            android:strokeColor="#FFF4DA" android:strokeLineCap="butt"
            android:strokeLineJoin="miter" android:strokeWidth="3.04"/>
        <path android:fillAlpha="0" android:fillColor="#FF000000"
            android:pathData="M83.89,18.43C110.55,18.43 132.21,39.77 132.21,66.07C132.21,92.36 110.55,113.7 83.89,113.7C57.22,113.7 35.57,92.36 35.57,66.07C35.57,39.77 57.22,18.43 83.89,18.43z"
            android:strokeColor="#FFDB77" android:strokeLineCap="butt"
            android:strokeLineJoin="miter" android:strokeWidth="4.04"/>
        <path android:fillColor="#FFC82F" android:pathData="M83.9,23.8C107.56,23.8 126.77,42.74 126.77,66.07C126.77,89.39 107.56,108.33 83.9,108.33C60.24,108.33 41.02,89.39 41.02,66.07C41.02,42.74 60.24,23.8 83.9,23.8z"/>
        <path android:fillColor="#ACE8FF" android:pathData="M135.59,211.82C135.59,211.82 118,277.51 118,277.51C117.5,279.37 115.57,280.48 113.71,279.98C113.71,279.98 113.71,279.98 113.71,279.98C111.84,279.48 110.73,277.56 111.23,275.69C111.23,275.69 128.83,210.01 128.83,210.01C129.33,208.14 131.25,207.04 133.12,207.54C133.12,207.54 133.12,207.54 133.12,207.54C134.99,208.04 136.09,209.96 135.59,211.82z"/>
        <path android:fillColor="#ACE8FF" android:pathData="M127.3,178.25C127.3,178.25 113.58,229.44 113.58,229.44C113.08,231.31 111.16,232.42 109.29,231.92C109.29,231.92 109.29,231.92 109.29,231.92C107.43,231.42 106.32,229.5 106.82,227.63C106.82,227.63 120.54,176.44 120.54,176.44C121.04,174.57 122.96,173.46 124.82,173.96C124.82,173.96 124.82,173.96 124.82,173.96C126.69,174.46 127.8,176.38 127.3,178.25z"/>
        <path android:fillColor="#ACE8FF" android:pathData="M171.25,195.31C171.25,195.31 157.53,246.51 157.53,246.51C157.03,248.37 155.11,249.48 153.25,248.98C153.25,248.98 153.25,248.98 153.25,248.98C151.38,248.48 150.27,246.56 150.77,244.7C150.77,244.7 164.49,193.5 164.49,193.5C164.99,191.64 166.91,190.53 168.78,191.03C168.78,191.03 168.78,191.03 168.78,191.03C170.64,191.53 171.75,193.45 171.25,195.31z"/>
        <path android:fillColor="#ACE8FF" android:pathData="M200.55,175.03C200.55,175.03 197.18,187.59 197.18,187.59C196.68,189.46 194.76,190.57 192.89,190.07C192.89,190.07 192.89,190.07 192.89,190.07C191.03,189.57 189.92,187.64 190.42,185.78C190.42,185.78 193.78,173.22 193.78,173.22C194.28,171.36 196.2,170.25 198.07,170.75C198.07,170.75 198.07,170.75 198.07,170.75C199.94,171.25 201.05,173.17 200.55,175.03z"/>
        <path android:fillColor="#ACE8FF" android:pathData="M90.71,191.46C90.71,191.46 87.34,204.01 87.34,204.01C86.84,205.88 84.92,206.99 83.06,206.49C83.06,206.49 83.06,206.49 83.06,206.49C81.19,205.99 80.08,204.07 80.58,202.2C80.58,202.2 83.95,189.64 83.95,189.64C84.45,187.78 86.37,186.67 88.23,187.17C88.23,187.17 88.23,187.17 88.23,187.17C90.1,187.67 91.21,189.59 90.71,191.46z"/>
        <group>
            <clip-path android:pathData="M53.46,55.24L225.43,55.24L225.43,161.7L53.46,161.7z"/>
            <path android:fillColor="#E8E8E8" android:pathData="M157.24,161.01C157.24,161.01 90.63,161.01 90.63,161.01C70.09,161.01 53.46,144.38 53.46,123.86C53.46,103.33 70.09,86.69 90.63,86.69C96.82,86.69 102.65,88.2 107.79,90.88C114.95,70.14 134.63,55.24 157.81,55.24C187.01,55.24 210.69,78.92 210.69,108.13C210.69,108.73 210.68,109.32 210.66,109.91C219.18,114.5 224.99,123.5 224.99,133.86C224.99,148.85 212.83,161.01 197.83,161.01C197.83,161.01 157.81,161.01 157.81,161.01C157.73,161.01 157.24,161.01 157.24,161.01C157.24,161.01 157.81,161.01 157.81,161.01C157.81,161.01 157.24,161.01 157.24,161.01z"/>
        </group>
    </group>
</vector>
