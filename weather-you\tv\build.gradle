plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'com.google.gms.google-services'
    id 'kotlinx-serialization'
    id 'com.google.firebase.crashlytics'
    alias(libs.plugins.compose.compiler)
}

def keyStorePropertiesFile = rootProject.file("keystore.properties")
//def keyStoreProperties = new Properties()
//keyStoreProperties.load(new FileInputStream(keyStorePropertiesFile))

android {
    namespace "com.rodrigmatrix.weatheryou.tv"
    compileSdk Sdk.compileSdk

    defaultConfig {
        applicationId Sdk.applicationId
        minSdk Sdk.tvMinSdk
        targetSdk Sdk.targetSdk
        versionCode computeVersionCode()
        versionName computeVersionName()

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
        javaCompileOptions {
            annotationProcessorOptions {
                arguments += [
                        "room.schemaLocation":"$projectDir/schemas".toString(),
                        "room.incremental":"true",
                        "room.expandProjection":"true"]
            }
        }
    }
    signingConfigs {
//        release {
//            keyAlias keyStoreProperties['releaseKeyAlias']
//            keyPassword keyStoreProperties['releaseKeyPassword']
//            storeFile file(keyStoreProperties['releaseKeyStore'])
//            storePassword keyStoreProperties['releaseStorePassword']
//        }
    }
    buildTypes {
        release {
//            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            applicationIdSuffix ".debug"
            debuggable true
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    kotlin.sourceSets.configureEach {
        languageSettings.optIn("kotlin.RequiresOptIn")
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion libs.versions.composeCompilerVersion.get()
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
}

static def computeVersionName() {
    return String.format('%d.%d.%d', Sdk.tvVersionMajor, Sdk.tvVersionMinor, Sdk.tvVersionPatch)
}

static def computeVersionCode() {
    return ((Sdk.tvVersionMajor * 100000)
    + (Sdk.tvVersionMinor * 10000)
    + (Sdk.tvVersionPatch * 1000)
    + Integer.valueOf(System.getenv("CIRCLE_BUILD_NUM") ?: Sdk.localVersionCode))
}

dependencies {
    implementation project(LocalModules.domain)
    implementation project(LocalModules.data)
    implementation project(LocalModules.core)
    implementation project(LocalModules.ads)
    implementation(libs.androidx.browser)

    implementation project(LocalModules.components)
    implementation project(LocalModules.locationDetails)
    implementation project(LocalModules.home)
    implementation project(LocalModules.settings)
    implementation project(LocalModules.addLocation)
    implementation project(LocalModules.about)

    implementation(libs.android.material)

    implementation(libs.androidx.appcompat)
    implementation(libs.androidx.ktx)
    implementation(libs.androidx.lifecycle)
    implementation(libs.androidx.window)

    implementation(libs.kotlin.stdlib)
    implementation(libs.kotlin.coroutines.android)
    implementation(libs.kotlin.serialization)

     implementation platform(libs.compose.bom)
    implementation(libs.compose.ui)
    implementation(libs.compose.runtime)
    implementation(libs.compose.preview)
    implementation(libs.compose.activity)
    implementation(libs.compose.navigation)
    implementation(libs.accompanist.permissions)
    implementation(libs.compose.tv.foundation)
    implementation(libs.compose.tv.material)
    implementation(libs.compose.material3)
    implementation(libs.compose.material)
    implementation(libs.jodaTime)

    implementation(libs.koin.android)
    implementation(libs.koin.compose)

    implementation(libs.coil)

    implementation platform(libs.firebase.bom)
    implementation(libs.firebase.remote.config)
    implementation(libs.firebase.crashlytics)
    implementation(libs.firebase.analytics)

    debugImplementation(libs.composeUiTooling)
    debugImplementation(libs.composeTestManifest)

    testImplementation(libs.junit)
    androidTestImplementation(libs.testExtJunit)
    androidTestImplementation(libs.espresso.core)
    androidTestImplementation(libs.composeUiTestJunit)
}