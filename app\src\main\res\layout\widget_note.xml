<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/LinearLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingStart="16dp"
    android:paddingTop="16dp"
    android:paddingEnd="16dp"
    android:theme="@style/AppTheme">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        >
        <TextView
            android:id="@+id/Title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="sans-serif-medium"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
            android:textColor="?android:textColorPrimary" />

        <ImageView
            android:id="@+id/ChangeNote"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:contentDescription="ChangeNote"
            android:src="@drawable/document_scanner" />
    </LinearLayout>

    <TextView
        android:id="@+id/Note"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:lineSpacingMultiplier="1.1"
        android:paddingBottom="16dp"
        android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
        android:textColor="?android:textColorPrimary" />

</LinearLayout>