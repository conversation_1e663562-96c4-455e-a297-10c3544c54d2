<resources>
    <style name="AppTheme" parent="Theme.Material3.Dark.NoActionBar">
        <item name="colorPrimary">@color/md_theme_primary</item>
        <item name="colorOnPrimary">@color/md_theme_scrim</item>
        <item name="colorPrimaryContainer">@color/md_theme_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_onPrimaryContainer</item>
        <item name="colorSecondary">@color/md_theme_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_onSecondaryContainer</item>
        <item name="colorTertiary">@color/md_theme_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_onTertiaryContainer</item>
        <item name="colorError">@color/md_theme_error</item>
        <item name="colorOnError">@color/md_theme_onError</item>
        <item name="colorErrorContainer">@color/md_theme_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_onErrorContainer</item>
        <item name="android:colorBackground">@color/md_theme_scrim</item>
        <item name="colorOnBackground">@color/md_theme_onBackground</item>
        <item name="colorSurface">@color/md_theme_scrim</item>
        <item name="colorOnSurface">@color/md_theme_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_scrim</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_outlineVariant</item>
        <item name="colorSurfaceInverse">@color/md_theme_inverseSurface</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_inverseOnSurface</item>
        <item name="colorPrimaryInverse">@color/md_theme_inversePrimary</item>
        <item name="colorPrimaryFixed">@color/md_theme_primaryFixed</item>
        <item name="colorOnPrimaryFixed">@color/md_theme_onPrimaryFixed</item>
        <item name="colorPrimaryFixedDim">@color/md_theme_primaryFixedDim</item>
        <item name="colorOnPrimaryFixedVariant">@color/md_theme_onPrimaryFixedVariant</item>
        <item name="colorSecondaryFixed">@color/md_theme_secondaryFixed</item>
        <item name="colorOnSecondaryFixed">@color/md_theme_onSecondaryFixed</item>
        <item name="colorSecondaryFixedDim">@color/md_theme_secondaryFixedDim</item>
        <item name="colorOnSecondaryFixedVariant">@color/md_theme_onSecondaryFixedVariant</item>
        <item name="colorTertiaryFixed">@color/md_theme_tertiaryFixed</item>
        <item name="colorOnTertiaryFixed">@color/md_theme_onTertiaryFixed</item>
        <item name="colorTertiaryFixedDim">@color/md_theme_tertiaryFixedDim</item>
        <item name="colorOnTertiaryFixedVariant">@color/md_theme_onTertiaryFixedVariant</item>
        <item name="colorSurfaceDim">@color/md_theme_surfaceDim</item>
        <item name="colorSurfaceBright">@color/md_theme_surfaceBright</item>
        <item name="colorSurfaceContainerLowest">@color/md_theme_scrim</item>
        <item name="colorSurfaceContainerLow">@color/md_theme_scrim</item>
        <item name="colorSurfaceContainer">@color/md_theme_surfaceContainer</item>
        <item name="colorSurfaceContainerHigh">@color/md_theme_surfaceContainerHigh</item>
        <item name="colorSurfaceContainerHighest">@color/md_theme_surfaceContainerHighest</item>

        <item name="android:statusBarColor">?attr/colorSurface</item>
        <item name="android:navigationBarColor">?attr/colorSurface</item>

        <item name="elevationOverlayEnabled">false</item>
        <item name="materialAlertDialogTheme">@style/Dialog.Dark</item>
        <item name="popupMenuBackground">@color/md_theme_surfaceContainerLow</item>
    </style>

    <style name="Dialog.Dark" parent="ThemeOverlay.Material3.MaterialAlertDialog">
        <item name="android:background">@color/md_theme_surfaceContainerLow</item>
    </style>
</resources>
