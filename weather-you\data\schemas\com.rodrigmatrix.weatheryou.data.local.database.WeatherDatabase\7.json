{"formatVersion": 1, "database": {"version": 7, "identityHash": "0febf1676b6549995c3223a98fb8398c", "entities": [{"tableName": "locations", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `latitude` REAL NOT NULL, `longitude` REAL NOT NULL, `name` TEXT NOT NULL, `countryCode` TEXT NOT NULL, `timezone` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "latitude", "columnName": "latitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "longitude", "columnName": "longitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "countryCode", "columnName": "countryCode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timezone", "columnName": "timezone", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "weatherWidgetLocationCache", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`name` TEXT NOT NULL, `latitude` REAL NOT NULL, `longitude` REAL NOT NULL, `currentWeather` REAL NOT NULL, `currentCondition` TEXT NOT NULL, `isDaylight` INTEGER NOT NULL, `countryCode` TEXT NOT NULL, `timezone` TEXT NOT NULL, `sunrise` TEXT NOT NULL, `sunset` TEXT NOT NULL, `maxWeather` REAL NOT NULL, `minWeather` REAL NOT NULL, `lastUpdate` TEXT NOT NULL, `days` TEXT NOT NULL, `hours` TEXT NOT NULL, `id` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "latitude", "columnName": "latitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "longitude", "columnName": "longitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "current<PERSON><PERSON><PERSON>", "columnName": "current<PERSON><PERSON><PERSON>", "affinity": "REAL", "notNull": true}, {"fieldPath": "currentCondition", "columnName": "currentCondition", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isDaylight", "columnName": "isDaylight", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "countryCode", "columnName": "countryCode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timezone", "columnName": "timezone", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sunrise", "columnName": "sunrise", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sunset", "columnName": "sunset", "affinity": "TEXT", "notNull": true}, {"fieldPath": "max<PERSON><PERSON><PERSON>", "columnName": "max<PERSON><PERSON><PERSON>", "affinity": "REAL", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "affinity": "REAL", "notNull": true}, {"fieldPath": "lastUpdate", "columnName": "lastUpdate", "affinity": "TEXT", "notNull": true}, {"fieldPath": "days", "columnName": "days", "affinity": "TEXT", "notNull": true}, {"fieldPath": "hours", "columnName": "hours", "affinity": "TEXT", "notNull": true}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '0febf1676b6549995c3223a98fb8398c')"]}}