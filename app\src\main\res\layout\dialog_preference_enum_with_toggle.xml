<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="8dp"
    android:paddingStart="26dp"
    android:paddingEnd="26dp"
    >

    <TextView
      android:id="@+id/EnumHint"
      android:visibility="gone"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      style="@style/MaterialAlertDialog.Material3.Body.Text"
      android:paddingBottom="8dp"
      />

    <RadioGroup
        android:id="@+id/EnumRadioGroup"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        />

    <com.google.android.material.checkbox.MaterialCheckBox
      android:id="@+id/Toggle"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      />


</LinearLayout>