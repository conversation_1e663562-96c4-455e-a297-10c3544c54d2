{"formatVersion": 1, "database": {"version": 3, "identityHash": "9e4aaeaaaf6edd7a6a4663126b6c87e7", "entities": [{"tableName": "locations", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `latitude` REAL NOT NULL, `longitude` REAL NOT NULL, `name` TEXT NOT NULL, `countryCode` TEXT NOT NULL, `timezone` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "latitude", "columnName": "latitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "longitude", "columnName": "longitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "countryCode", "columnName": "countryCode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timezone", "columnName": "timezone", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "weatherWidgetLocationCache", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`name` TEXT NOT NULL, `latitude` REAL NOT NULL, `longitude` REAL NOT NULL, `currentWeather` REAL NOT NULL, `currentCondition` TEXT NOT NULL, `isDaylight` INTEGER NOT NULL, `maxWeather` REAL NOT NULL, `minWeather` REAL NOT NULL, `lastUpdate` TEXT NOT NULL, `nextHourWeather` REAL NOT NULL, `nextHourCondition` TEXT NOT NULL, `nextHourIsDaylight` INTEGER NOT NULL, `nextTwoHoursWeather` REAL NOT NULL, `nextTwoHoursCondition` TEXT NOT NULL, `nextTwoHoursIsDaylight` INTEGER NOT NULL, `nextThreeHoursWeather` REAL NOT NULL, `nextThreeHoursCondition` TEXT NOT NULL, `nextThreeHoursIsDaylight` INTEGER NOT NULL, `nextFourHoursWeather` REAL NOT NULL, `nextFourHoursCondition` TEXT NOT NULL, `nextFourHoursIsDaylight` INTEGER NOT NULL, `tomorrowMaxWeather` REAL NOT NULL, `tomorrowMinWeather` REAL NOT NULL, `tomorrowCondition` TEXT NOT NULL, `nextTwoDaysMaxWeather` REAL NOT NULL, `nextTwoDaysMinWeather` REAL NOT NULL, `nextTwoDaysCondition` TEXT NOT NULL, `id` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "latitude", "columnName": "latitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "longitude", "columnName": "longitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "current<PERSON><PERSON><PERSON>", "columnName": "current<PERSON><PERSON><PERSON>", "affinity": "REAL", "notNull": true}, {"fieldPath": "currentCondition", "columnName": "currentCondition", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isDaylight", "columnName": "isDaylight", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "max<PERSON><PERSON><PERSON>", "columnName": "max<PERSON><PERSON><PERSON>", "affinity": "REAL", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "affinity": "REAL", "notNull": true}, {"fieldPath": "lastUpdate", "columnName": "lastUpdate", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nextHour<PERSON><PERSON>her", "columnName": "nextHour<PERSON><PERSON>her", "affinity": "REAL", "notNull": true}, {"fieldPath": "nextHourCondition", "columnName": "nextHourCondition", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nextHourIsDaylight", "columnName": "nextHourIsDaylight", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "nextTwoHoursWeather", "columnName": "nextTwoHoursWeather", "affinity": "REAL", "notNull": true}, {"fieldPath": "nextTwoHoursCondition", "columnName": "nextTwoHoursCondition", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nextTwoHoursIsDaylight", "columnName": "nextTwoHoursIsDaylight", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "nextThreeHoursWeather", "columnName": "nextThreeHoursWeather", "affinity": "REAL", "notNull": true}, {"fieldPath": "nextThreeHoursCondition", "columnName": "nextThreeHoursCondition", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nextThreeHoursIsDaylight", "columnName": "nextThreeHoursIsDaylight", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "nextFourHoursWeather", "columnName": "nextFourHoursWeather", "affinity": "REAL", "notNull": true}, {"fieldPath": "nextFourHoursCondition", "columnName": "nextFourHoursCondition", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nextFourHoursIsDaylight", "columnName": "nextFourHoursIsDaylight", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tomorrowMaxWeather", "columnName": "tomorrowMaxWeather", "affinity": "REAL", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "affinity": "REAL", "notNull": true}, {"fieldPath": "tomorrowCondition", "columnName": "tomorrowCondition", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nextTwoDaysMaxWeather", "columnName": "nextTwoDaysMaxWeather", "affinity": "REAL", "notNull": true}, {"fieldPath": "nextTwoDaysMinWeather", "columnName": "nextTwoDaysMinWeather", "affinity": "REAL", "notNull": true}, {"fieldPath": "nextTwoDaysCondition", "columnName": "nextTwoDaysCondition", "affinity": "TEXT", "notNull": true}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '9e4aaeaaaf6edd7a6a4663126b6c87e7')"]}}