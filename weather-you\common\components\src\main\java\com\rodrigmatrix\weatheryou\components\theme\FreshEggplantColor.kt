package com.rodrigmatrix.weatheryou.components.theme

import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.ui.graphics.Color

private val primaryLight = Color(0xFF9B006A)
private val onPrimaryLight = Color(0xFFFFFFFF)
private val primaryContainerLight = Color(0xFFD62296)
private val onPrimaryContainerLight = Color(0xFFFFFFFF)
private val secondaryLight = Color(0xFF9B3B71)
private val onSecondaryLight = Color(0xFFFFFFFF)
private val secondaryContainerLight = Color(0xFFFF9ACD)
private val onSecondaryContainerLight = Color(0xFF5C003D)
private val tertiaryLight = Color(0xFFA50007)
private val onTertiaryLight = Color(0xFFFFFFFF)
private val tertiaryContainerLight = Color(0xFFDC3127)
private val onTertiaryContainerLight = Color(0xFFFFFFFF)
private val errorLight = Color(0xFFBA1A1A)
private val onErrorLight = Color(0xFFFFFFFF)
private val errorContainerLight = Color(0xFFFFDAD6)
private val onErrorContainerLight = Color(0xFF410002)
private val backgroundLight = Color(0xFFFFF8F8)
private val onBackgroundLight = Color(0xFF25181E)
private val surfaceLight = Color(0xFFFFF8F8)
private val onSurfaceLight = Color(0xFF25181E)
private val surfaceVariantLight = Color(0xFFFBDAE7)
private val onSurfaceVariantLight = Color(0xFF57404B)
private val outlineLight = Color(0xFF8B707B)
private val outlineVariantLight = Color(0xFFDEBECB)
private val scrimLight = Color(0xFF000000)
private val inverseSurfaceLight = Color(0xFF3B2C33)
private val inverseOnSurfaceLight = Color(0xFFFFECF2)
private val inversePrimaryLight = Color(0xFFFFAFD5)
private val surfaceDimLight = Color(0xFFECD4DD)
private val surfaceBrightLight = Color(0xFFFFF8F8)
private val surfaceContainerLowestLight = Color(0xFFFFFFFF)
private val surfaceContainerLowLight = Color(0xFFFFF0F4)
private val surfaceContainerLight = Color(0xFFFFE8F0)
private val surfaceContainerHighLight = Color(0xFFFBE2EB)
private val surfaceContainerHighestLight = Color(0xFFF5DCE5)
private val primaryLightMediumContrast = Color(0xFF830059)
private val onPrimaryLightMediumContrast = Color(0xFFFFFFFF)
private val primaryContainerLightMediumContrast = Color(0xFFD62296)
private val onPrimaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val secondaryLightMediumContrast = Color(0xFF781D54)
private val onSecondaryLightMediumContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightMediumContrast = Color(0xFFB65188)
private val onSecondaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryLightMediumContrast = Color(0xFF8C0005)
private val onTertiaryLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightMediumContrast = Color(0xFFDC3127)
private val onTertiaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val errorLightMediumContrast = Color(0xFF8C0009)
private val onErrorLightMediumContrast = Color(0xFFFFFFFF)
private val errorContainerLightMediumContrast = Color(0xFFDA342E)
private val onErrorContainerLightMediumContrast = Color(0xFFFFFFFF)
private val backgroundLightMediumContrast = Color(0xFFFFF8F8)
private val onBackgroundLightMediumContrast = Color(0xFF25181E)
private val surfaceLightMediumContrast = Color(0xFFFFF8F8)
private val onSurfaceLightMediumContrast = Color(0xFF25181E)
private val surfaceVariantLightMediumContrast = Color(0xFFFBDAE7)
private val onSurfaceVariantLightMediumContrast = Color(0xFF533C47)
private val outlineLightMediumContrast = Color(0xFF715863)
private val outlineVariantLightMediumContrast = Color(0xFF8F737F)
private val scrimLightMediumContrast = Color(0xFF000000)
private val inverseSurfaceLightMediumContrast = Color(0xFF3B2C33)
private val inverseOnSurfaceLightMediumContrast = Color(0xFFFFECF2)
private val inversePrimaryLightMediumContrast = Color(0xFFFFAFD5)
private val surfaceDimLightMediumContrast = Color(0xFFECD4DD)
private val surfaceBrightLightMediumContrast = Color(0xFFFFF8F8)
private val surfaceContainerLowestLightMediumContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightMediumContrast = Color(0xFFFFF0F4)
private val surfaceContainerLightMediumContrast = Color(0xFFFFE8F0)
private val surfaceContainerHighLightMediumContrast = Color(0xFFFBE2EB)
private val surfaceContainerHighestLightMediumContrast = Color(0xFFF5DCE5)
private val primaryLightHighContrast = Color(0xFF490030)
private val onPrimaryLightHighContrast = Color(0xFFFFFFFF)
private val primaryContainerLightHighContrast = Color(0xFF830059)
private val onPrimaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val secondaryLightHighContrast = Color(0xFF490030)
private val onSecondaryLightHighContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightHighContrast = Color(0xFF781D54)
private val onSecondaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryLightHighContrast = Color(0xFF4E0001)
private val onTertiaryLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightHighContrast = Color(0xFF8C0005)
private val onTertiaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val errorLightHighContrast = Color(0xFF4E0002)
private val onErrorLightHighContrast = Color(0xFFFFFFFF)
private val errorContainerLightHighContrast = Color(0xFF8C0009)
private val onErrorContainerLightHighContrast = Color(0xFFFFFFFF)
private val backgroundLightHighContrast = Color(0xFFFFF8F8)
private val onBackgroundLightHighContrast = Color(0xFF25181E)
private val surfaceLightHighContrast = Color(0xFFFFF8F8)
private val onSurfaceLightHighContrast = Color(0xFF000000)
private val surfaceVariantLightHighContrast = Color(0xFFFBDAE7)
private val onSurfaceVariantLightHighContrast = Color(0xFF321E27)
private val outlineLightHighContrast = Color(0xFF533C47)
private val outlineVariantLightHighContrast = Color(0xFF533C47)
private val scrimLightHighContrast = Color(0xFF000000)
private val inverseSurfaceLightHighContrast = Color(0xFF3B2C33)
private val inverseOnSurfaceLightHighContrast = Color(0xFFFFFFFF)
private val inversePrimaryLightHighContrast = Color(0xFFFFE6EF)
private val surfaceDimLightHighContrast = Color(0xFFECD4DD)
private val surfaceBrightLightHighContrast = Color(0xFFFFF8F8)
private val surfaceContainerLowestLightHighContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightHighContrast = Color(0xFFFFF0F4)
private val surfaceContainerLightHighContrast = Color(0xFFFFE8F0)
private val surfaceContainerHighLightHighContrast = Color(0xFFFBE2EB)
private val surfaceContainerHighestLightHighContrast = Color(0xFFF5DCE5)
private val primaryDark = Color(0xFFFFAFD5)
private val onPrimaryDark = Color(0xFF620042)
private val primaryContainerDark = Color(0xFFD62296)
private val onPrimaryContainerDark = Color(0xFFFFFFFF)
private val secondaryDark = Color(0xFFFFAFD5)
private val onSecondaryDark = Color(0xFF600541)
private val secondaryContainerDark = Color(0xFF741A51)
private val onSecondaryContainerDark = Color(0xFFFFC5DE)
private val tertiaryDark = Color(0xFFFFB4AA)
private val onTertiaryDark = Color(0xFF690003)
private val tertiaryContainerDark = Color(0xFFDC3127)
private val onTertiaryContainerDark = Color(0xFFFFFFFF)
private val errorDark = Color(0xFFFFB4AB)
private val onErrorDark = Color(0xFF690005)
private val errorContainerDark = Color(0xFF93000A)
private val onErrorContainerDark = Color(0xFFFFDAD6)
private val backgroundDark = Color(0xFF1C1016)
private val onBackgroundDark = Color(0xFFF5DCE5)
private val surfaceDark = Color(0xFF1C1016)
private val onSurfaceDark = Color(0xFFF5DCE5)
private val surfaceVariantDark = Color(0xFF57404B)
private val onSurfaceVariantDark = Color(0xFFDEBECB)
private val outlineDark = Color(0xFFA68995)
private val outlineVariantDark = Color(0xFF57404B)
private val scrimDark = Color(0xFF000000)
private val inverseSurfaceDark = Color(0xFFF5DCE5)
private val inverseOnSurfaceDark = Color(0xFF3B2C33)
private val inversePrimaryDark = Color(0xFFB5007D)
private val surfaceDimDark = Color(0xFF1C1016)
private val surfaceBrightDark = Color(0xFF45353C)
private val surfaceContainerLowestDark = Color(0xFF170A10)
private val surfaceContainerLowDark = Color(0xFF25181E)
private val surfaceContainerDark = Color(0xFF2A1C22)
private val surfaceContainerHighDark = Color(0xFF35262C)
private val surfaceContainerHighestDark = Color(0xFF403037)
private val primaryDarkMediumContrast = Color(0xFFFFB5D8)
private val onPrimaryDarkMediumContrast = Color(0xFF330020)
private val primaryContainerDarkMediumContrast = Color(0xFFFB46B4)
private val onPrimaryContainerDarkMediumContrast = Color(0xFF000000)
private val secondaryDarkMediumContrast = Color(0xFFFFB5D8)
private val onSecondaryDarkMediumContrast = Color(0xFF330020)
private val secondaryContainerDarkMediumContrast = Color(0xFFD86DA6)
private val onSecondaryContainerDarkMediumContrast = Color(0xFF000000)
private val tertiaryDarkMediumContrast = Color(0xFFFFBAB0)
private val onTertiaryDarkMediumContrast = Color(0xFF370001)
private val tertiaryContainerDarkMediumContrast = Color(0xFFFF5545)
private val onTertiaryContainerDarkMediumContrast = Color(0xFF000000)
private val errorDarkMediumContrast = Color(0xFFFFBAB1)
private val onErrorDarkMediumContrast = Color(0xFF370001)
private val errorContainerDarkMediumContrast = Color(0xFFFF5449)
private val onErrorContainerDarkMediumContrast = Color(0xFF000000)
private val backgroundDarkMediumContrast = Color(0xFF1C1016)
private val onBackgroundDarkMediumContrast = Color(0xFFF5DCE5)
private val surfaceDarkMediumContrast = Color(0xFF1C1016)
private val onSurfaceDarkMediumContrast = Color(0xFFFFF9F9)
private val surfaceVariantDarkMediumContrast = Color(0xFF57404B)
private val onSurfaceVariantDarkMediumContrast = Color(0xFFE2C2CF)
private val outlineDarkMediumContrast = Color(0xFFB99BA7)
private val outlineVariantDarkMediumContrast = Color(0xFF977C87)
private val scrimDarkMediumContrast = Color(0xFF000000)
private val inverseSurfaceDarkMediumContrast = Color(0xFFF5DCE5)
private val inverseOnSurfaceDarkMediumContrast = Color(0xFF35262C)
private val inversePrimaryDarkMediumContrast = Color(0xFF8C0060)
private val surfaceDimDarkMediumContrast = Color(0xFF1C1016)
private val surfaceBrightDarkMediumContrast = Color(0xFF45353C)
private val surfaceContainerLowestDarkMediumContrast = Color(0xFF170A10)
private val surfaceContainerLowDarkMediumContrast = Color(0xFF25181E)
private val surfaceContainerDarkMediumContrast = Color(0xFF2A1C22)
private val surfaceContainerHighDarkMediumContrast = Color(0xFF35262C)
private val surfaceContainerHighestDarkMediumContrast = Color(0xFF403037)
private val primaryDarkHighContrast = Color(0xFFFFF9F9)
private val onPrimaryDarkHighContrast = Color(0xFF000000)
private val primaryContainerDarkHighContrast = Color(0xFFFFB5D8)
private val onPrimaryContainerDarkHighContrast = Color(0xFF000000)
private val secondaryDarkHighContrast = Color(0xFFFFF9F9)
private val onSecondaryDarkHighContrast = Color(0xFF000000)
private val secondaryContainerDarkHighContrast = Color(0xFFFFB5D8)
private val onSecondaryContainerDarkHighContrast = Color(0xFF000000)
private val tertiaryDarkHighContrast = Color(0xFFFFF9F8)
private val onTertiaryDarkHighContrast = Color(0xFF000000)
private val tertiaryContainerDarkHighContrast = Color(0xFFFFBAB0)
private val onTertiaryContainerDarkHighContrast = Color(0xFF000000)
private val errorDarkHighContrast = Color(0xFFFFF9F9)
private val onErrorDarkHighContrast = Color(0xFF000000)
private val errorContainerDarkHighContrast = Color(0xFFFFBAB1)
private val onErrorContainerDarkHighContrast = Color(0xFF000000)
private val backgroundDarkHighContrast = Color(0xFF1C1016)
private val onBackgroundDarkHighContrast = Color(0xFFF5DCE5)
private val surfaceDarkHighContrast = Color(0xFF1C1016)
private val onSurfaceDarkHighContrast = Color(0xFFFFFFFF)
private val surfaceVariantDarkHighContrast = Color(0xFF57404B)
private val onSurfaceVariantDarkHighContrast = Color(0xFFFFF9F9)
private val outlineDarkHighContrast = Color(0xFFE2C2CF)
private val outlineVariantDarkHighContrast = Color(0xFFE2C2CF)
private val scrimDarkHighContrast = Color(0xFF000000)
private val inverseSurfaceDarkHighContrast = Color(0xFFF5DCE5)
private val inverseOnSurfaceDarkHighContrast = Color(0xFF000000)
private val inversePrimaryDarkHighContrast = Color(0xFF57003A)
private val surfaceDimDarkHighContrast = Color(0xFF1C1016)
private val surfaceBrightDarkHighContrast = Color(0xFF45353C)
private val surfaceContainerLowestDarkHighContrast = Color(0xFF170A10)
private val surfaceContainerLowDarkHighContrast = Color(0xFF25181E)
private val surfaceContainerDarkHighContrast = Color(0xFF2A1C22)
private val surfaceContainerHighDarkHighContrast = Color(0xFF35262C)
private val surfaceContainerHighestDarkHighContrast = Color(0xFF403037)


val freshEggplantLightScheme = lightColorScheme(
    primary = primaryLight,
    onPrimary = onPrimaryLight,
    primaryContainer = primaryContainerLight,
    onPrimaryContainer = onPrimaryContainerLight,
    secondary = secondaryLight,
    onSecondary = onSecondaryLight,
    secondaryContainer = secondaryContainerLight,
    onSecondaryContainer = onSecondaryContainerLight,
    tertiary = tertiaryLight,
    onTertiary = onTertiaryLight,
    tertiaryContainer = tertiaryContainerLight,
    onTertiaryContainer = onTertiaryContainerLight,
    error = errorLight,
    onError = onErrorLight,
    errorContainer = errorContainerLight,
    onErrorContainer = onErrorContainerLight,
    background = backgroundLight,
    onBackground = onBackgroundLight,
    surface = surfaceLight,
    onSurface = onSurfaceLight,
    surfaceVariant = surfaceVariantLight,
    onSurfaceVariant = onSurfaceVariantLight,
    outline = outlineLight,
    outlineVariant = outlineVariantLight,
    scrim = scrimLight,
    inverseSurface = inverseSurfaceLight,
    inverseOnSurface = inverseOnSurfaceLight,
    inversePrimary = inversePrimaryLight,
    surfaceDim = surfaceDimLight,
    surfaceBright = surfaceBrightLight,
    surfaceContainerLowest = surfaceContainerLowestLight,
    surfaceContainerLow = surfaceContainerLowLight,
    surfaceContainer = surfaceContainerLight,
    surfaceContainerHigh = surfaceContainerHighLight,
    surfaceContainerHighest = surfaceContainerHighestLight,
)

val freshEggplantDarkScheme = darkColorScheme(
    primary = primaryDark,
    onPrimary = onPrimaryDark,
    primaryContainer = primaryContainerDark,
    onPrimaryContainer = onPrimaryContainerDark,
    secondary = secondaryDark,
    onSecondary = onSecondaryDark,
    secondaryContainer = secondaryContainerDark,
    onSecondaryContainer = onSecondaryContainerDark,
    tertiary = tertiaryDark,
    onTertiary = onTertiaryDark,
    tertiaryContainer = tertiaryContainerDark,
    onTertiaryContainer = onTertiaryContainerDark,
    error = errorDark,
    onError = onErrorDark,
    errorContainer = errorContainerDark,
    onErrorContainer = onErrorContainerDark,
    background = backgroundDark,
    onBackground = onBackgroundDark,
    surface = surfaceDark,
    onSurface = onSurfaceDark,
    surfaceVariant = surfaceVariantDark,
    onSurfaceVariant = onSurfaceVariantDark,
    outline = outlineDark,
    outlineVariant = outlineVariantDark,
    scrim = scrimDark,
    inverseSurface = inverseSurfaceDark,
    inverseOnSurface = inverseOnSurfaceDark,
    inversePrimary = inversePrimaryDark,
    surfaceDim = surfaceDimDark,
    surfaceBright = surfaceBrightDark,
    surfaceContainerLowest = surfaceContainerLowestDark,
    surfaceContainerLow = surfaceContainerLowDark,
    surfaceContainer = surfaceContainerDark,
    surfaceContainerHigh = surfaceContainerHighDark,
    surfaceContainerHighest = surfaceContainerHighestDark,
)