package io.despicable.chromeos.presentation.worker

import android.content.Context
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import androidx.glance.appwidget.GlanceAppWidgetManager
import io.despicable.chromeos.domain.usecase.GetWeatherDataUseCase
import io.despicable.chromeos.presentation.ui.widget.WeatherWidget
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

/**
 * Worker for updating weather data in the background
 */
class WeatherUpdateWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params), KoinComponent {

    companion object {
        private const val TAG = "WeatherUpdateWorker"
    }

    private val getWeatherDataUseCase: GetWeatherDataUseCase by inject()

    override suspend fun doWork(): Result {
        Log.d(TAG, "WeatherUpdateWorker started")
        return try {
            // Refresh weather data
            Log.d(TAG, "Refreshing weather data...")
            getWeatherDataUseCase.refreshWeatherData()
            Log.d(TAG, "Weather data refreshed successfully")

            // Update all weather widgets using state-based approach
            Log.d(TAG, "Updating all weather widgets...")
            val glanceManager = GlanceAppWidgetManager(applicationContext)
            val glanceIds = glanceManager.getGlanceIds(WeatherWidget::class.java)

            Log.d(TAG, "Found ${glanceIds.size} weather widgets to update")

            val weatherWidget = WeatherWidget()
            glanceIds.forEach { glanceId ->
                Log.d(TAG, "Updating widget with GlanceId: $glanceId")
                weatherWidget.updateWidgetState(applicationContext, glanceId)
            }

            Log.d(TAG, "All weather widgets updated successfully")

            Result.success()
        } catch (e: Exception) {
            Log.e(TAG, "Error in WeatherUpdateWorker", e)
            Result.retry()
        }
    }
}
