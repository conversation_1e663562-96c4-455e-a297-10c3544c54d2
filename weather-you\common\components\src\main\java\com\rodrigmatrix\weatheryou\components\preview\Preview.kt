package com.rodrigmatrix.weatheryou.components.preview

import com.rodrigmatrix.weatheryou.weathericons.R
import com.rodrigmatrix.weatheryou.domain.model.*
import org.joda.time.DateTime

val PreviewWeatherLocation = WeatherLocation(
    id = 0,
    widgetId = "",
    orderIndex = 0,
    name = "Toronto",
    latitude = 0.0,
    longitude = 0.0,
    isCurrentLocation = true,
    currentWeather = 10.0,
    feelsLike = -2.0,
    maxTemperature = 10.0,
    lowestTemperature = 0.0,
    currentCondition = WeatherCondition.Snow,
    currentTime = DateTime(),
    timeZone = "",
    precipitationProbability = 60.0,
    precipitationType = PrecipitationType.Clear,
    humidity = 87.0,
    dewPoint = 22.0,
    windSpeed = 0.0,
    windDirection = 0.0,
    uvIndex = 0.0,
    sunrise = DateTime(),
    sunset = DateTime(),
    visibility = 0.0,
    pressure = 0.0,
    expirationDate = DateTime(),
    minWeekTemperature = 0.0,
    maxWeekTemperature = 0.0,
    cloudCover = 0.0,
    countryCode = "",
)

val PreviewWeatherList = listOf(
    WeatherLocation(
        id = 0,
        widgetId = "",
        orderIndex = 0,
        name = "Toronto",
        latitude = 0.0,
        longitude = 0.0,
        isCurrentLocation = false,
        currentWeather = 1.0,
        feelsLike = -2.0,
        maxTemperature = 10.0,
        lowestTemperature = 0.0,
        currentCondition = WeatherCondition.Snow,
        currentTime = DateTime(),
        timeZone = "",
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        humidity = 87.0,
        dewPoint = 22.0,
        windSpeed = 0.0,
        windDirection = 0.0,
        uvIndex = 0.0,
        sunrise = DateTime(),
        sunset = DateTime(),
        visibility = 0.0,
        pressure = 0.0,
        expirationDate = DateTime(),
        minWeekTemperature = 0.0,
        maxWeekTemperature = 0.0,
        cloudCover = 0.0,
        countryCode = "",
    ),
    WeatherLocation(
        id = 1,
        widgetId = "",
        orderIndex = 0,
        name = "Montreal",
        latitude = 0.0,
        longitude = 0.0,
        isCurrentLocation = false,
        currentWeather = -10.0,
        feelsLike = -2.0,
        maxTemperature = 10.0,
        lowestTemperature = 0.0,
        currentCondition = WeatherCondition.Snow,
        currentTime = DateTime(),
        timeZone = "",
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        humidity = 87.0,
        dewPoint = 22.0,
        windSpeed = 0.0,
        windDirection = 0.0,
        uvIndex = 0.0,
        sunrise = DateTime(),
        sunset = DateTime(),
        visibility = 0.0,
        pressure = 0.0,
        expirationDate = DateTime(),
        minWeekTemperature = 0.0,
        maxWeekTemperature = 0.0,
        cloudCover = 0.0,
        countryCode = "",
    ),
    WeatherLocation(
        id = 2,
        widgetId = "",
        orderIndex = 0,
        name = "Montreal",
        latitude = 0.0,
        longitude = 0.0,
        isCurrentLocation = false,
        currentWeather = -10.0,
        feelsLike = -2.0,
        maxTemperature = 10.0,
        lowestTemperature = 0.0,
        currentCondition = WeatherCondition.Snow,
        currentTime = DateTime(),
        timeZone = "",
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        humidity = 87.0,
        dewPoint = 22.0,
        windSpeed = 0.0,
        windDirection = 0.0,
        uvIndex = 0.0,
        sunrise = DateTime(),
        sunset = DateTime(),
        visibility = 0.0,
        pressure = 0.0,
        expirationDate = DateTime(),
        minWeekTemperature = 0.0,
        maxWeekTemperature = 0.0,
        cloudCover = 0.0,
        countryCode = "",
    ),
    WeatherLocation(
        id = 3,
        widgetId = "",
        orderIndex = 0,
        name = "Montreal",
        latitude = 0.0,
        longitude = 0.0,
        isCurrentLocation = false,
        currentWeather = -10.0,
        feelsLike = -2.0,
        maxTemperature = 10.0,
        lowestTemperature = 0.0,
        currentCondition = WeatherCondition.Snow,
        currentTime = DateTime(),
        timeZone = "",
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        humidity = 87.0,
        dewPoint = 22.0,
        windSpeed = 0.0,
        windDirection = 0.0,
        uvIndex = 0.0,
        sunrise = DateTime(),
        sunset = DateTime(),
        visibility = 0.0,
        pressure = 0.0,
        expirationDate = DateTime(),
        minWeekTemperature = 0.0,
        maxWeekTemperature = 0.0,
        cloudCover = 0.0,
        countryCode = "",
    ),
    WeatherLocation(
        id = 4,
        widgetId = "",
        orderIndex = 0,
        name = "Montreal",
        latitude = 0.0,
        longitude = 0.0,
        isCurrentLocation = false,
        currentWeather = -10.0,
        feelsLike = -2.0,
        maxTemperature = 10.0,
        lowestTemperature = 0.0,
        currentCondition = WeatherCondition.Snow,
        currentTime = DateTime(),
        timeZone = "",
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        humidity = 87.0,
        dewPoint = 22.0,
        windSpeed = 0.0,
        windDirection = 0.0,
        uvIndex = 0.0,
        sunrise = DateTime(),
        sunset = DateTime(),
        visibility = 0.0,
        pressure = 0.0,
        expirationDate = DateTime(),
        minWeekTemperature = 0.0,
        maxWeekTemperature = 0.0,
        cloudCover = 0.0,
        countryCode = "",
    ),
    WeatherLocation(
        id = 5,
        widgetId = "",
        orderIndex = 0,
        name = "Montreal",
        latitude = 0.0,
        longitude = 0.0,
        isCurrentLocation = false,
        currentWeather = -10.0,
        feelsLike = -2.0,
        maxTemperature = 10.0,
        lowestTemperature = 0.0,
        currentCondition = WeatherCondition.Snow,
        currentTime = DateTime(),
        timeZone = "",
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        humidity = 87.0,
        dewPoint = 22.0,
        windSpeed = 0.0,
        windDirection = 0.0,
        uvIndex = 0.0,
        sunrise = DateTime(),
        sunset = DateTime(),
        visibility = 0.0,
        pressure = 0.0,
        expirationDate = DateTime(),
        minWeekTemperature = 0.0,
        maxWeekTemperature = 0.0,
        cloudCover = 0.0,
        countryCode = "",
    ),
    WeatherLocation(
        id = 6,
        widgetId = "",
        orderIndex = 0,
        name = "Montreal",
        latitude = 0.0,
        longitude = 0.0,
        isCurrentLocation = false,
        currentWeather = -10.0,
        feelsLike = -2.0,
        maxTemperature = 10.0,
        lowestTemperature = 0.0,
        currentCondition = WeatherCondition.Snow,
        currentTime = DateTime(),
        timeZone = "",
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        humidity = 87.0,
        dewPoint = 22.0,
        windSpeed = 0.0,
        windDirection = 0.0,
        uvIndex = 0.0,
        sunrise = DateTime(),
        sunset = DateTime(),
        visibility = 0.0,
        pressure = 0.0,
        expirationDate = DateTime(),
        minWeekTemperature = 0.0,
        maxWeekTemperature = 0.0,
        cloudCover = 0.0,
        countryCode = "",
    ),
    WeatherLocation(
        id = 7,
        widgetId = "",
        orderIndex = 0,
        name = "Montreal",
        latitude = 0.0,
        longitude = 0.0,
        isCurrentLocation = false,
        currentWeather = -10.0,
        feelsLike = -2.0,
        maxTemperature = 10.0,
        lowestTemperature = 0.0,
        currentCondition = WeatherCondition.Snow,
        currentTime = DateTime(),
        timeZone = "",
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        humidity = 87.0,
        dewPoint = 22.0,
        windSpeed = 0.0,
        windDirection = 0.0,
        uvIndex = 0.0,
        sunrise = DateTime(),
        sunset = DateTime(),
        visibility = 0.0,
        pressure = 0.0,
        expirationDate = DateTime(),
        minWeekTemperature = 0.0,
        maxWeekTemperature = 0.0,
        cloudCover = 0.0,
        countryCode = "",
    ),
    WeatherLocation(
        id = 8,
        widgetId = "",
        orderIndex = 0,
        name = "Montreal",
        latitude = 0.0,
        longitude = 0.0,
        isCurrentLocation = false,
        currentWeather = -10.0,
        feelsLike = -2.0,
        maxTemperature = 10.0,
        lowestTemperature = 0.0,
        currentCondition = WeatherCondition.Snow,
        currentTime = DateTime(),
        timeZone = "",
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        humidity = 87.0,
        dewPoint = 22.0,
        windSpeed = 0.0,
        windDirection = 0.0,
        uvIndex = 0.0,
        sunrise = DateTime(),
        sunset = DateTime(),
        visibility = 0.0,
        pressure = 0.0,
        expirationDate = DateTime(),
        minWeekTemperature = 0.0,
        maxWeekTemperature = 0.0,
        cloudCover = 0.0,
        countryCode = "",
    )
)

val PreviewHourlyForecast = listOf(
    WeatherHour(
        temperature = 1.0,
        weatherCondition = WeatherCondition.Snow,
        dateTime = DateTime(),
        precipitationProbability = 60.0,
        precipitationType = PrecipitationType.Snow,
        cloudCover = 40.0,
        feelsLike = 1.0,
        humidity = 0.0,
        visibility = 0.0,
        windSpeed = 0.0,
        windDirection = 0,
        uvIndex = 0.0,
        snowfallIntensity = 0.0,
        precipitationAmount = 0.0,
    ),
    WeatherHour(
        temperature = 9.0,
        weatherCondition = WeatherCondition.Snow,
        dateTime = DateTime(),
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        cloudCover = 40.0,
        feelsLike = 1.0,
        humidity = 0.0,
        visibility = 0.0,
        windSpeed = 0.0,
        windDirection = 0,
        uvIndex = 0.0,
        snowfallIntensity = 0.0,
        precipitationAmount = 0.0,
    ),
    WeatherHour(
        temperature = 9.0,
        weatherCondition = WeatherCondition.Snow,
        dateTime = DateTime(),
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        cloudCover = 40.0,
        feelsLike = 1.0,
        humidity = 0.0,
        visibility = 0.0,
        windSpeed = 0.0,
        windDirection = 0,
        uvIndex = 0.0,
        snowfallIntensity = 0.0,
        precipitationAmount = 0.0,
    ),
    WeatherHour(
        temperature = 8.0,
        weatherCondition = WeatherCondition.Snow,
        dateTime = DateTime(),
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        cloudCover = 40.0,
        feelsLike = 1.0,
        humidity = 0.0,
        visibility = 0.0,
        windSpeed = 0.0,
        windDirection = 0,
        uvIndex = 0.0,
        snowfallIntensity = 0.0,
        precipitationAmount = 0.0,
    ),
    WeatherHour(
        temperature = 6.0,
        weatherCondition = WeatherCondition.Snow,
        dateTime = DateTime(),
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        cloudCover = 40.0,
        feelsLike = 1.0,
        humidity = 0.0,
        visibility = 0.0,
        windSpeed = 0.0,
        windDirection = 0,
        uvIndex = 0.0,
        snowfallIntensity = 0.0,
        precipitationAmount = 0.0,
    ),
    WeatherHour(
        temperature = 6.0,
        weatherCondition = WeatherCondition.Snow,
        dateTime = DateTime(),
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        cloudCover = 40.0,
        feelsLike = 1.0,
        humidity = 0.0,
        visibility = 0.0,
        windSpeed = 0.0,
        windDirection = 0,
        uvIndex = 0.0,
        snowfallIntensity = 0.0,
        precipitationAmount = 0.0,
    ),
    WeatherHour(
        temperature = 5.0,
        weatherCondition = WeatherCondition.Snow,
        dateTime = DateTime(),
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        cloudCover = 40.0,
        feelsLike = 1.0,
        humidity = 0.0,
        visibility = 0.0,
        windSpeed = 0.0,
        windDirection = 0,
        uvIndex = 0.0,
        snowfallIntensity = 0.0,
        precipitationAmount = 0.0,
    ),
    WeatherHour(
        temperature = 2.0,
        weatherCondition = WeatherCondition.Snow,
        dateTime = DateTime(),
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        cloudCover = 40.0,
        feelsLike = 1.0,
        humidity = 0.0,
        visibility = 0.0,
        windSpeed = 0.0,
        windDirection = 0,
        uvIndex = 0.0,
        snowfallIntensity = 0.0,
        precipitationAmount = 0.0,
    )
)

val PreviewFutureDaysForecast = listOf(
    WeatherDay(
        dateTime = DateTime(),
        weatherCondition = WeatherCondition.Snow,
        temperature = 10.0,
        maxTemperature = 14.0,
        minTemperature = 1.0,
        hours = PreviewHourlyForecast,
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        windSpeed = 0.0,
        humidity = 0.0,
        sunrise = DateTime(),
        sunset = DateTime(),
        precipitationAmount = 0.0,
        snowfallAmount = 0.0,
        solarMidnight = DateTime(),
        solarNoon = DateTime(),
        moonPhase = MoonPhase.New,
        sunsetAstronomical = DateTime(),
        sunriseAstronomical = DateTime(),
        sunsetNautical = DateTime(),
        sunriseNautical = DateTime(),
        sunsetCivil = DateTime(),
        sunriseCivil = DateTime(),
    ),
    WeatherDay(
        dateTime = DateTime(),
        weatherCondition = WeatherCondition.Snow,
        temperature = 10.0,
        maxTemperature = 14.0,
        minTemperature = 1.0,
        hours = PreviewHourlyForecast,
        precipitationProbability = 80.0,
        precipitationType = PrecipitationType.Rain,
        windSpeed = 0.0,
        humidity = 0.0,
        sunrise = DateTime(),
        sunset = DateTime(),
        precipitationAmount = 0.0,
        snowfallAmount = 0.0,
        solarMidnight = DateTime(),
        solarNoon = DateTime(),
        moonPhase = MoonPhase.New,
        sunsetAstronomical = DateTime(),
        sunriseAstronomical = DateTime(),
        sunsetNautical = DateTime(),
        sunriseNautical = DateTime(),
        sunsetCivil = DateTime(),
        sunriseCivil = DateTime(),
    ),
    WeatherDay(
        dateTime = DateTime(),
        weatherCondition = WeatherCondition.Snow,
        temperature = 10.0,
        maxTemperature = 14.0,
        minTemperature = 1.0,
        hours = PreviewHourlyForecast,
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        windSpeed = 0.0,
        humidity = 0.0,
        sunrise = DateTime(),
        sunset = DateTime(),
        precipitationAmount = 0.0,
        snowfallAmount = 0.0,
        solarMidnight = DateTime(),
        solarNoon = DateTime(),
        moonPhase = MoonPhase.New,
        sunsetAstronomical = DateTime(),
        sunriseAstronomical = DateTime(),
        sunsetNautical = DateTime(),
        sunriseNautical = DateTime(),
        sunsetCivil = DateTime(),
        sunriseCivil = DateTime(),
    ),
    WeatherDay(
        dateTime = DateTime(),
        weatherCondition = WeatherCondition.Snow,
        temperature = 10.0,
        maxTemperature = 14.0,
        minTemperature = 1.0,
        hours = PreviewHourlyForecast,
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        windSpeed = 0.0,
        humidity = 0.0,
        sunrise = DateTime(),
        sunset = DateTime(),
        precipitationAmount = 0.0,
        snowfallAmount = 0.0,
        solarMidnight = DateTime(),
        solarNoon = DateTime(),
        moonPhase = MoonPhase.New,
        sunsetAstronomical = DateTime(),
        sunriseAstronomical = DateTime(),
        sunsetNautical = DateTime(),
        sunriseNautical = DateTime(),
        sunsetCivil = DateTime(),
        sunriseCivil = DateTime(),
    ),
    WeatherDay(
        dateTime = DateTime(),
        weatherCondition = WeatherCondition.Snow,
        temperature = 10.0,
        maxTemperature = 14.0,
        minTemperature = 1.0,
        hours = PreviewHourlyForecast,
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        windSpeed = 0.0,
        humidity = 0.0,
        sunrise = DateTime(),
        sunset = DateTime(),
        precipitationAmount = 0.0,
        snowfallAmount = 0.0,
        solarMidnight = DateTime(),
        solarNoon = DateTime(),
        moonPhase = MoonPhase.New,
        sunsetAstronomical = DateTime(),
        sunriseAstronomical = DateTime(),
        sunsetNautical = DateTime(),
        sunriseNautical = DateTime(),
        sunsetCivil = DateTime(),
        sunriseCivil = DateTime(),
    ),
    WeatherDay(
        dateTime = DateTime(),
        weatherCondition = WeatherCondition.Snow,
        temperature = 10.0,
        maxTemperature = 14.0,
        minTemperature = 1.0,
        hours = PreviewHourlyForecast,
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        windSpeed = 0.0,
        humidity = 0.0,
        sunrise = DateTime(),
        sunset = DateTime(),
        precipitationAmount = 0.0,
        snowfallAmount = 0.0,
        solarMidnight = DateTime(),
        solarNoon = DateTime(),
        moonPhase = MoonPhase.New,
        sunsetAstronomical = DateTime(),
        sunriseAstronomical = DateTime(),
        sunsetNautical = DateTime(),
        sunriseNautical = DateTime(),
        sunsetCivil = DateTime(),
        sunriseCivil = DateTime(),
    ),
    WeatherDay(
        dateTime = DateTime(),
        weatherCondition = WeatherCondition.Snow,
        temperature = 10.0,
        maxTemperature = 14.0,
        minTemperature = 1.0,
        hours = PreviewHourlyForecast,
        precipitationProbability = 0.0,
        precipitationType = PrecipitationType.Clear,
        windSpeed = 0.0,
        humidity = 0.0,
        sunrise = DateTime(),
        sunset = DateTime(),
        precipitationAmount = 0.0,
        snowfallAmount = 0.0,
        solarMidnight = DateTime(),
        solarNoon = DateTime(),
        moonPhase = MoonPhase.New,
        sunsetAstronomical = DateTime(),
        sunriseAstronomical = DateTime(),
        sunsetNautical = DateTime(),
        sunriseNautical = DateTime(),
        sunsetCivil = DateTime(),
        sunriseCivil = DateTime(),
    )
)