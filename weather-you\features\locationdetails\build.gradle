plugins {
    id("com.android.library")
    id("org.jetbrains.kotlin.android")
    alias(libs.plugins.compose.compiler)
}

android {
    namespace = "com.rodrigmatrix.weatheryou.locationdetails"
    compileSdk Sdk.compileSdk

    defaultConfig {
        minSdk Sdk.phoneMinSdk
        targetSdk Sdk.targetSdk

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    kotlin.sourceSets.configureEach {
        languageSettings.optIn("kotlin.RequiresOptIn")
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion libs.versions.composeCompilerVersion.get()
    }
}

dependencies {
    implementation project(LocalModules.domain)
    implementation project(LocalModules.core)
    implementation project(LocalModules.components)
    implementation project(LocalModules.weatherIcons)

    implementation(libs.androidx.ktx)
    implementation(libs.androidx.lifecycle)
    implementation(libs.androidx.window)

    implementation(libs.kotlin.stdlib)
    implementation(libs.kotlin.coroutines.android)
    implementation(libs.kotlin.serialization)
    implementation (libs.compose.charts)

    implementation platform(libs.compose.bom)
    implementation(libs.compose.ui)
    implementation(libs.compose.runtime)
    implementation(libs.compose.material3)
    implementation(libs.compose.material)
    implementation(libs.androidx.adaptive)
    implementation(libs.androidx.adaptive.layout)
    implementation(libs.androidx.adaptive.navigation)
    implementation(libs.androidx.adaptive.navigation.suite)
    implementation(libs.compose.preview)
    implementation(libs.compose.activity)
    implementation(libs.compose.navigation)
    implementation(libs.compose.constraint.layout)
    implementation(libs.compose.navigation)
    implementation(libs.compose.window.size)
    implementation(libs.accompanist.permissions)
    implementation(libs.accompanist.navigation)
    implementation(libs.accompanist.adaptive)
    implementation(libs.androidx.browser)

    implementation(libs.koin.android)
    implementation(libs.koin.compose)

    implementation(libs.jodaTime)

    implementation(libs.coil)

    implementation(libs.lottie.compose)

    debugImplementation(libs.composeUiTooling)
    debugImplementation(libs.composeTestManifest)

    testImplementation(libs.junit)
    androidTestImplementation(libs.testExtJunit)
    androidTestImplementation(libs.espresso.core)
    androidTestImplementation(libs.composeUiTestJunit)
}