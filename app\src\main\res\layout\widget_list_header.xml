<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/LinearLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingStart="16dp"
    android:paddingTop="16dp"
    android:paddingEnd="16dp"
    android:theme="@style/AppTheme">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        >
        <TextView
            android:id="@+id/Title"
            android:layout_width="match_parent"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:fontFamily="sans-serif-medium"
            android:paddingBottom="16dp"
            android:textAppearance="?attr/textAppearanceBodyLarge"
            android:textColor="?android:textColorPrimary" />

        <ImageView
            android:id="@+id/ChangeNote"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:contentDescription="ChangeNote"
          android:src="@drawable/document_scanner" />

    </LinearLayout>

</LinearLayout>