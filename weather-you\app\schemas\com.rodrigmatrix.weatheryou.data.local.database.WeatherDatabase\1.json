{"formatVersion": 1, "database": {"version": 1, "identityHash": "06fc094a38be48a18f48644253b8c408", "entities": [{"tableName": "locations", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`latitude` REAL NOT NULL, `longitude` REAL NOT NULL, `name` TEXT NOT NULL, PRIMARY KEY(`latitude`, `longitude`))", "fields": [{"fieldPath": "latitude", "columnName": "latitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "longitude", "columnName": "longitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["latitude", "longitude"], "autoGenerate": false}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '06fc094a38be48a18f48644253b8c408')"]}}