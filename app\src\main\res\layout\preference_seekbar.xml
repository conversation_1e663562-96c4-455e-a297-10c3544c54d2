<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingStart="4dp"
    android:paddingTop="16dp"
    android:paddingEnd="4dp"
    android:paddingBottom="4dp">

    <TextView
        android:id="@+id/Title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:textAppearance="?attr/textAppearanceBodyLarge" />

    <com.google.android.material.slider.Slider
        android:id="@+id/Slider"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:saveEnabled="false"
        android:stepSize="1"
        app:thumbRadius="8dp" />

</LinearLayout>