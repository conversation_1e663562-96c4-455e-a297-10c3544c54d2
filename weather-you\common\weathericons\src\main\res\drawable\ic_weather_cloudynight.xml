<vector android:height="24dp" android:viewportHeight="256"
    android:viewportWidth="256" android:width="24dp" xmlns:android="http://schemas.android.com/apk/res/android">
    <group>
        <clip-path android:pathData="M0,0h256v256h-256z"/>
        <path android:fillColor="#FFC82F" android:pathData="M108.5,39C103.7,49 100.9,60.3 100.9,72.1C100.9,114.4 135.3,148.6 177.7,148.6C192.8,148.6 206.9,144.2 218.8,136.7C214,182 175.4,217.4 128.6,217.4C78.6,217.4 38,177 38,127.1C38,84.1 68.1,48.1 108.5,39z"/>
        <path android:fillColor="#FFC82F" android:pathData="M127,41.5C130.59,42.59 133.4,45.41 134.5,49C135.6,45.41 138.41,42.6 142,41.5C138.41,40.4 135.6,37.59 134.5,34C133.4,37.59 130.59,40.4 127,41.5z"/>
        <path android:fillColor="#FFC82F" android:pathData="M22,165C25.35,166.02 27.98,168.65 29,172C30.02,168.65 32.65,166.02 36,165C32.65,163.98 30.02,161.35 29,158C27.98,161.35 25.35,163.98 22,165z"/>
        <path android:fillColor="#FFC82F" android:pathData="M191,209C195.31,210.31 198.69,213.69 200,218C201.32,213.69 204.69,210.32 209,209C204.69,207.68 201.32,204.31 200,200C198.69,204.31 195.31,207.68 191,209z"/>
        <path android:fillColor="#E8E8E8" android:pathData="M106.29,233.64C106.29,233.64 59.49,233.64 59.49,233.64C45.08,233.64 33.39,222.37 33.39,208.47C33.39,194.57 45.08,183.3 59.49,183.3C63.84,183.3 67.94,184.32 71.55,186.14C76.57,172.09 90.4,162 106.68,162C127.19,162 143.82,178.04 143.82,197.82C143.82,198.22 143.81,198.63 143.8,199.03C149.79,202.13 153.86,208.23 153.86,215.24C153.86,225.4 145.32,233.64 134.79,233.64C134.79,233.64 106.68,233.64 106.68,233.64C106.62,233.64 106.29,233.64 106.29,233.64C106.29,233.64 106.68,233.64 106.68,233.64C106.68,233.64 106.29,233.64 106.29,233.64z"/>
        <path android:fillAlpha="0" android:fillColor="#FF000000"
            android:pathData="M106.29,233.64C106.29,233.64 59.49,233.64 59.49,233.64C45.08,233.64 33.39,222.37 33.39,208.47C33.39,194.57 45.08,183.3 59.49,183.3C63.84,183.3 67.94,184.32 71.55,186.14C76.57,172.09 90.4,162 106.68,162C127.19,162 143.82,178.04 143.82,197.82C143.82,198.22 143.81,198.63 143.8,199.03C149.79,202.13 153.86,208.23 153.86,215.24C153.86,225.4 145.32,233.64 134.79,233.64C134.79,233.64 106.68,233.64 106.68,233.64C106.62,233.64 106.29,233.64 106.29,233.64C106.29,233.64 106.68,233.64 106.68,233.64C106.68,233.64 106.29,233.64 106.29,233.64z"
            android:strokeColor="#FFFFFF" android:strokeLineCap="butt"
            android:strokeLineJoin="miter" android:strokeWidth="7"/>
        <path android:fillColor="#E8E8E8" android:pathData="M197.99,107.1C197.99,107.1 162.65,107.1 162.65,107.1C151.77,107.1 142.94,98.59 142.94,88.09C142.94,77.59 151.77,69.08 162.65,69.08C165.94,69.08 169.04,69.86 171.76,71.23C175.55,60.62 186,53 198.29,53C213.78,53 226.34,65.11 226.34,80.05C226.34,80.36 226.33,80.66 226.32,80.96C230.84,83.31 233.92,87.91 233.92,93.21C233.92,100.88 227.47,107.1 219.51,107.1C219.51,107.1 198.29,107.1 198.29,107.1C198.24,107.1 197.99,107.1 197.99,107.1C197.99,107.1 198.29,107.1 198.29,107.1C198.29,107.1 197.99,107.1 197.99,107.1z"/>
        <path android:fillAlpha="0" android:fillColor="#FF000000"
            android:pathData="M197.99,107.1C197.99,107.1 162.65,107.1 162.65,107.1C151.77,107.1 142.94,98.59 142.94,88.09C142.94,77.59 151.77,69.08 162.65,69.08C165.94,69.08 169.04,69.86 171.76,71.23C175.55,60.62 186,53 198.29,53C213.78,53 226.34,65.11 226.34,80.05C226.34,80.36 226.33,80.66 226.32,80.96C230.84,83.31 233.92,87.91 233.92,93.21C233.92,100.88 227.47,107.1 219.51,107.1C219.51,107.1 198.29,107.1 198.29,107.1C198.24,107.1 197.99,107.1 197.99,107.1C197.99,107.1 198.29,107.1 198.29,107.1C198.29,107.1 197.99,107.1 197.99,107.1z"
            android:strokeColor="#FFFFFF" android:strokeLineCap="butt"
            android:strokeLineJoin="miter" android:strokeWidth="7"/>
        <path android:fillColor="#E8E8E8" android:pathData="M55.55,90.27C55.55,90.27 25.5,90.27 25.5,90.27C16.24,90.27 8.74,83.03 8.74,74.11C8.74,65.18 16.24,57.95 25.5,57.95C28.29,57.95 30.93,58.6 33.24,59.77C36.47,50.75 45.35,44.27 55.8,44.27C68.97,44.27 79.65,54.57 79.65,67.27C79.65,67.53 79.65,67.79 79.64,68.05C83.48,70.04 86.1,73.96 86.1,78.46C86.1,84.98 80.62,90.27 73.85,90.27C73.85,90.27 55.8,90.27 55.8,90.27C55.76,90.27 55.55,90.27 55.55,90.27C55.55,90.27 55.8,90.27 55.8,90.27C55.8,90.27 55.55,90.27 55.55,90.27z"/>
        <path android:fillAlpha="0" android:fillColor="#FF000000"
            android:pathData="M55.55,90.27C55.55,90.27 25.5,90.27 25.5,90.27C16.24,90.27 8.74,83.03 8.74,74.11C8.74,65.18 16.24,57.95 25.5,57.95C28.29,57.95 30.93,58.6 33.24,59.77C36.47,50.75 45.35,44.27 55.8,44.27C68.97,44.27 79.65,54.57 79.65,67.27C79.65,67.53 79.65,67.79 79.64,68.05C83.48,70.04 86.1,73.96 86.1,78.46C86.1,84.98 80.62,90.27 73.85,90.27C73.85,90.27 55.8,90.27 55.8,90.27C55.76,90.27 55.55,90.27 55.55,90.27C55.55,90.27 55.8,90.27 55.8,90.27C55.8,90.27 55.55,90.27 55.55,90.27z"
            android:strokeColor="#FFFFFF" android:strokeLineCap="butt"
            android:strokeLineJoin="miter" android:strokeWidth="6"/>
    </group>
</vector>
