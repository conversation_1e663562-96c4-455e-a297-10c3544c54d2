<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:startDestination="@+id/Notes">

    <fragment
        android:id="@+id/Notes"
        android:name="com.philkes.notallyx.presentation.activity.main.fragment.NotesFragment"
        android:label="@string/notes">

        <action
            android:id="@+id/NotesToSearch"
            app:destination="@id/Search"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim" />
    </fragment>

    <fragment
        android:id="@+id/Labels"
        android:name="com.philkes.notallyx.presentation.activity.main.fragment.LabelsFragment"
        android:label="@string/labels">

        <action
            android:id="@+id/LabelsToDisplayLabel"
            app:destination="@id/DisplayLabel"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"
            app:popEnterAnim="@anim/nav_default_pop_enter_anim"
            app:popExitAnim="@anim/nav_default_pop_exit_anim" />

    </fragment>

    <fragment
        android:id="@+id/Deleted"
        android:name="com.philkes.notallyx.presentation.activity.main.fragment.DeletedFragment"
      android:label="@string/deleted" >

        <action
          android:id="@+id/DeletedToSearch"
          app:destination="@id/Search"
          app:enterAnim="@anim/nav_default_enter_anim"
          app:exitAnim="@anim/nav_default_exit_anim"
          app:popEnterAnim="@anim/nav_default_pop_enter_anim"
          app:popExitAnim="@anim/nav_default_pop_exit_anim" />
    </fragment>

    <fragment
        android:id="@+id/Archived"
        android:name="com.philkes.notallyx.presentation.activity.main.fragment.ArchivedFragment"
      android:label="@string/archived" >
        <action
          android:id="@+id/ArchivedToSearch"
          app:destination="@id/Search"
          app:enterAnim="@anim/nav_default_enter_anim"
          app:exitAnim="@anim/nav_default_exit_anim"
          app:popEnterAnim="@anim/nav_default_pop_enter_anim"
          app:popExitAnim="@anim/nav_default_pop_exit_anim" />
    </fragment>

    <fragment
      android:id="@+id/Reminders"
      android:name="com.philkes.notallyx.presentation.activity.main.fragment.RemindersFragment"
      android:label="@string/reminders" />

    <fragment
        android:id="@+id/Settings"
        android:name="com.philkes.notallyx.presentation.activity.main.fragment.settings.SettingsFragment"
        android:label="@string/settings" />

    <fragment
        android:id="@+id/Search"
        android:name="com.philkes.notallyx.presentation.activity.main.fragment.SearchFragment">
        <argument
          android:name="initialFolder"
          app:argType="com.philkes.notallyx.data.model.Folder" />
    </fragment>

    <fragment
        android:id="@+id/DisplayLabel"
        android:name="com.philkes.notallyx.presentation.activity.main.fragment.DisplayLabelFragment"
        android:label="{notallyx.intent.extra.DISPLAYED_LABEL}" />

    <fragment
        android:id="@+id/Unlabeled"
        android:name="com.philkes.notallyx.presentation.activity.main.fragment.UnlabeledFragment"
        android:label="@string/unlabeled" />

</navigation>