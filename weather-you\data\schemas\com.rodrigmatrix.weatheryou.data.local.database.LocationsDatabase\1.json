{"formatVersion": 1, "database": {"version": 1, "identityHash": "4f205cd9d69e3c14b3d18e653bb04587", "entities": [{"tableName": "locations", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `latitude` REAL NOT NULL, `orderIndex` INTEGER NOT NULL, `longitude` REAL NOT NULL, `name` TEXT NOT NULL, `countryCode` TEXT NOT NULL, `timeZone` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "latitude", "columnName": "latitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "orderIndex", "columnName": "orderIndex", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "longitude", "columnName": "longitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "countryCode", "columnName": "countryCode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timeZone", "columnName": "timeZone", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "currentLocation", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`latitude` REAL NOT NULL, `longitude` REAL NOT NULL, `name` TEXT NOT NULL, `countryCode` TEXT NOT NULL, `timeZone` TEXT NOT NULL, `lastUpdate` TEXT NOT NULL, `id` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "latitude", "columnName": "latitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "longitude", "columnName": "longitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "countryCode", "columnName": "countryCode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timeZone", "columnName": "timeZone", "affinity": "TEXT", "notNull": true}, {"fieldPath": "lastUpdate", "columnName": "lastUpdate", "affinity": "TEXT", "notNull": true}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '4f205cd9d69e3c14b3d18e653bb04587')"]}}