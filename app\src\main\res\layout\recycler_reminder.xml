<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:baselineAligned="false"
  android:orientation="horizontal"
  android:layout_marginStart="8dp"
  android:weightSum="1">

  <LinearLayout
    android:layout_width="0dp"
    android:layout_height="wrap_content"
    android:layout_weight="1"
    android:orientation="vertical">

    <TextView
      android:id="@+id/DateTime"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_marginStart="8dp"
      android:layout_gravity="center_vertical"
      android:drawablePadding="8dp"
      app:drawableStartCompat="@drawable/event"
      android:maxLines="1"
      android:layout_marginBottom="4dp"
      android:layout_marginTop="4dp"
      android:textAppearance="?attr/textAppearanceBodyLarge" />

    <TextView
      android:id="@+id/Repetition"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_marginStart="8dp"
      android:layout_gravity="center_vertical"
      android:drawablePadding="8dp"
      app:drawableStartCompat="@drawable/schedule"
      android:maxLines="1"
      android:layout_marginBottom="4dp"
      android:textAppearance="?attr/textAppearanceBodyLarge" />
  </LinearLayout>

  <LinearLayout
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:layout_marginStart="16dp"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <ImageButton
      android:id="@+id/EditButton"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_marginStart="8dp"
      android:padding="8dp"
      android:background="?attr/selectableItemBackground"
      android:contentDescription="@string/edit"
      app:srcCompat="@drawable/edit"
      app:tint="?attr/colorControlNormal" />

    <ImageButton
      android:id="@+id/DeleteButton"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_marginStart="8dp"
      android:padding="8dp"
      android:background="?attr/selectableItemBackground"
      android:contentDescription="@string/delete"
      app:srcCompat="@drawable/delete"
      app:tint="?attr/colorControlNormal" />

  </LinearLayout>

</LinearLayout>
