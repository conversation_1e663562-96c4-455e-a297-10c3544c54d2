package io.despicable.chromeos.presentation.ui.config

import android.appwidget.AppWidgetManager
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.ui.Modifier
import androidx.glance.appwidget.GlanceAppWidgetManager
import androidx.lifecycle.lifecycleScope
import io.despicable.chromeos.domain.usecase.GetWeatherDataUseCase
import io.despicable.chromeos.domain.usecase.UpdateWidgetConfigUseCase
import io.despicable.chromeos.presentation.ui.widget.WeatherWidget
import io.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider
import io.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel
import io.despicable.chromeos.ui.theme.ChromeOSTheme
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.core.parameter.parametersOf

/**
 * Configuration activity for weather widget
 */
class WidgetConfigActivity : ComponentActivity() {

    companion object {
        private const val TAG = "WidgetConfigActivity"
    }

    private val getWeatherDataUseCase: GetWeatherDataUseCase by inject()
    private val updateWidgetConfigUseCase: UpdateWidgetConfigUseCase by inject()

    private var appWidgetId = AppWidgetManager.INVALID_APPWIDGET_ID

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // Get widget ID from intent
        appWidgetId = intent?.extras?.getInt(
            AppWidgetManager.EXTRA_APPWIDGET_ID,
            AppWidgetManager.INVALID_APPWIDGET_ID
        ) ?: AppWidgetManager.INVALID_APPWIDGET_ID

        // If widget ID is invalid, finish activity
        if (appWidgetId == AppWidgetManager.INVALID_APPWIDGET_ID) {
            finish()
            return
        }

        // Set result to CANCELED initially
        setResult(RESULT_CANCELED)

        // Create ViewModel using Koin
        val viewModel: WidgetConfigViewModel by inject { parametersOf(appWidgetId) }

        setContent {
            ChromeOSTheme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    WidgetConfigScreen(
                        viewModel = viewModel,
                        onSaveClicked = {
                            saveConfigurationAndFinish(viewModel)
                        },
                        modifier = Modifier.padding(innerPadding)
                    )
                }
            }
        }
    }

    private fun saveConfigurationAndFinish(viewModel: WidgetConfigViewModel) {
        lifecycleScope.launch {
            try {
                Log.d(TAG, "Starting widget configuration save process...")

                // First, save the widget configuration
                Log.d(TAG, "Saving widget configuration...")
                viewModel.saveConfiguration()
                Log.d(TAG, "Widget configuration saved")

                // Update the widget using state-based approach
                Log.d(TAG, "Updating widget state...")
                val glanceManager = GlanceAppWidgetManager(this@WidgetConfigActivity)
                val glanceId = glanceManager.getGlanceIdBy(appWidgetId)

                val weatherWidget = WeatherWidget()
                weatherWidget.updateWidgetState(this@WidgetConfigActivity, glanceId)
                Log.d(TAG, "Widget state updated successfully")

                // Also trigger the widget provider update
                Log.d(TAG, "Sending broadcast to update widget...")
                val updateIntent = Intent(this@WidgetConfigActivity, WeatherWidgetProvider::class.java).apply {
                    action = AppWidgetManager.ACTION_APPWIDGET_UPDATE
                    putExtra(AppWidgetManager.EXTRA_APPWIDGET_IDS, intArrayOf(appWidgetId))
                }
                sendBroadcast(updateIntent)
                Log.d(TAG, "Broadcast sent")

                Log.d(TAG, "Widget updated successfully")

                // Set result to OK
                val resultValue = Intent().apply {
                    putExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, appWidgetId)
                }
                setResult(RESULT_OK, resultValue)

                finish()
            } catch (e: Exception) {
                // Handle error
                Log.e(TAG, "Error updating widget", e)
                finish()
            }
        }
    }
}
