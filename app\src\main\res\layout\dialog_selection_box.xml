<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:paddingTop="8dp"
  android:paddingStart="26dp"
  android:paddingEnd="26dp"
  android:orientation="vertical">

  <TextView
    android:id="@+id/Message"
    style="@style/MaterialAlertDialog.Material3.Body.Text"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingBottom="8dp"/>

  <com.google.android.material.textfield.TextInputLayout
    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.textfield.MaterialAutoCompleteTextView
      android:id="@+id/SelectionBox"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:inputType="none" />
  </com.google.android.material.textfield.TextInputLayout>


</LinearLayout>