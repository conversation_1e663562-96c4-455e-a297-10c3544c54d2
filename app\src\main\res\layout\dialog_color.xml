<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="8dp"
    android:paddingBottom="8dp"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
  >
    <TextView
      android:id="@+id/Message"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:paddingStart="8dp"
      android:paddingEnd="8dp"
      android:layout_alignParentTop="true"
      android:layout_centerHorizontal="true"
      android:text="@string/change_color_message"
      android:layout_marginBottom="4dp"
      />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/MainListView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/Message"
        android:layout_centerHorizontal="true"
        android:clipToPadding="false"
        android:scrollIndicators="top"
        android:paddingBottom="8dp"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:spanCount="4" />

</RelativeLayout>