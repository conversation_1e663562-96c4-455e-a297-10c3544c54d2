<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:orientation="horizontal">

  <TextView
    android:id="@+id/LabelText"
    android:layout_width="0dp"
    android:layout_height="wrap_content"
    android:layout_weight="1"
    android:ellipsize="end"
    android:maxLines="1"
    android:padding="16dp"
    android:textAppearance="?attr/textAppearanceBodyLarge" />

  <ImageButton
    android:id="@+id/VisibilityButton"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:minHeight="48dp"
    android:layout_gravity="center_vertical"
    android:padding="8dp"
    android:background="?attr/selectableItemBackground"
    android:contentDescription="@string/labels_hidden_in_overview_title"
    app:srcCompat="@drawable/visibility"
    app:tint="?attr/colorControlNormal" />

  <ImageButton
    android:id="@+id/EditButton"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:minHeight="48dp"
    android:layout_gravity="center_vertical"
    android:padding="8dp"
    android:background="?attr/selectableItemBackground"
    android:contentDescription="@string/edit"
    app:srcCompat="@drawable/edit"
    app:tint="?attr/colorControlNormal" />

  <ImageButton
    android:id="@+id/DeleteButton"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:minHeight="48dp"
    android:layout_gravity="center_vertical"
    android:padding="8dp"
    android:background="?attr/selectableItemBackground"
    android:contentDescription="@string/delete"
    app:srcCompat="@drawable/delete"
    app:tint="?attr/colorControlNormal" />


</LinearLayout>